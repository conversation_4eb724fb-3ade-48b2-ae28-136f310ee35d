/**
 * iOS-specific utilities for handling mobile browser issues
 */

export interface IOSViewportState {
  originalViewport: string;
  originalScrollY: number;
  drawerBodyScrollTop: number;
  originalBodyStyles: {
    position: string;
    top: string;
    width: string;
    overflow: string;
  };
}

/**
 * Detects if the current device is iOS (iPhone, iPad, iPod)
 * Includes detection for iPad on iOS 13+ which reports as MacIntel
 */
export const isIOS = (): boolean => {
  if (typeof window === "undefined") return false;

  return (
    /iPad|iPhone|iPod/.test(navigator.userAgent) ||
    (navigator.platform === "MacIntel" && navigator.maxTouchPoints > 1)
  );
};

/**
 * Updates the viewport meta tag with the specified content
 */
export const updateViewportMeta = (content: string): void => {
  if (typeof document === "undefined") return;

  let viewport = document.querySelector('meta[name="viewport"]');
  if (!viewport) {
    viewport = document.createElement("meta");
    viewport.setAttribute("name", "viewport");
    document.head.appendChild(viewport);
  }
  viewport.setAttribute("content", content);
};

/**
 * Prevents iOS zoom and maintains header visibility when textarea/input is focused
 * Modified to allow scrolling within the drawer while preventing body scroll
 */
export const handleIOSTextareaFocus = (): IOSViewportState => {
  if (!isIOS() || typeof window === "undefined" || typeof document === "undefined") {
    return {
      originalViewport: "",
      originalScrollY: 0,
      drawerBodyScrollTop: 0,
      originalBodyStyles: { position: "", top: "", width: "", overflow: "" },
    };
  }

  // Store comprehensive original state
  const currentViewport = document.querySelector('meta[name="viewport"]');
  const originalViewport =
    currentViewport?.getAttribute("content") || "width=device-width, initial-scale=1.0";
  const originalScrollY = window.scrollY;

  // Store original body styles
  const originalBodyStyles = {
    position: document.body.style.position || "",
    top: document.body.style.top || "",
    width: document.body.style.width || "",
    overflow: document.body.style.overflow || "",
  };

  // Store drawer body scroll position
  const drawerBody = document.querySelector(".drawer-body");
  const drawerBodyScrollTop = drawerBody ? (drawerBody as HTMLElement).scrollTop : 0;

  // Set iOS-specific viewport to prevent zoom
  updateViewportMeta("width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no");

  // Prevent body scroll but allow drawer content to scroll
  document.body.classList.add("ios-textarea-focused");
  document.body.style.overflow = "hidden";
  document.body.style.position = "fixed";
  document.body.style.top = `-${originalScrollY}px`;
  document.body.style.width = "100%";

  // Ensure drawer header stays visible and drawer body can scroll
  const drawerHeader = document.querySelector(".drawer-header");

  // Apply styles directly without requestAnimationFrame to avoid timing issues
  if (drawerHeader) {
    (drawerHeader as HTMLElement).style.position = "sticky";
    (drawerHeader as HTMLElement).style.top = "0";
    (drawerHeader as HTMLElement).style.zIndex = "1000";
    (drawerHeader as HTMLElement).style.backgroundColor = "white";
    (drawerHeader as HTMLElement).classList.add("ios-fixed");
  }

  if (drawerBody) {
    // Enable scrolling within the drawer body
    (drawerBody as HTMLElement).style.overflowY = "auto";
    (drawerBody as HTMLElement).style.WebkitOverflowScrolling = "touch";
    (drawerBody as HTMLElement).style.maxHeight = "calc(100vh - 120px)"; // Account for header and some padding
    (drawerBody as HTMLElement).classList.add("ios-scrollable");
  }

  return { originalViewport, originalScrollY, drawerBodyScrollTop, originalBodyStyles };
};

/**
 * Restores the original viewport and scroll position when textarea/input loses focus
 * Conservative approach that restores exact original state without any layout changes
 */
export const handleIOSTextareaBlur = (state: IOSViewportState): void => {
  if (!isIOS() || typeof window === "undefined" || typeof document === "undefined") {
    return;
  }

  // Get references to elements
  const drawerHeader = document.querySelector(".drawer-header");
  const drawerBody = document.querySelector(".drawer-body");

  // Step 1: Restore viewport first (this has no visual impact)
  updateViewportMeta(state.originalViewport);

  // Step 2: Restore body styles to their exact original state
  document.body.classList.remove("ios-textarea-focused");
  document.body.style.position = state.originalBodyStyles.position;
  document.body.style.top = state.originalBodyStyles.top;
  document.body.style.width = state.originalBodyStyles.width;
  document.body.style.overflow = state.originalBodyStyles.overflow;

  // Step 3: Restore scroll position instantly (no smooth scrolling to avoid jumps)
  if (state.originalScrollY > 0) {
    window.scrollTo(0, state.originalScrollY);
  }

  // Step 4: Restore drawer elements to their original state
  if (drawerHeader) {
    (drawerHeader as HTMLElement).classList.remove("ios-fixed");
    (drawerHeader as HTMLElement).style.position = "";
    (drawerHeader as HTMLElement).style.top = "";
    (drawerHeader as HTMLElement).style.zIndex = "";
    (drawerHeader as HTMLElement).style.backgroundColor = "";
  }

  if (drawerBody) {
    (drawerBody as HTMLElement).classList.remove("ios-scrollable");
    (drawerBody as HTMLElement).style.overflowY = "";
    (drawerBody as HTMLElement).style.WebkitOverflowScrolling = "";
    (drawerBody as HTMLElement).style.maxHeight = "";

    // Restore the drawer's scroll position to exactly where it was
    (drawerBody as HTMLElement).scrollTop = state.drawerBodyScrollTop;
  }
};

/**
 * Gets the appropriate font size for iOS to prevent zoom
 */
export const getIOSFontSize = (defaultSize: string = "14px"): string => {
  return isIOS() ? "16px" : defaultSize;
};

/**
 * Gets iOS-specific styles for textarea/input elements
 */
export const getIOSInputStyles = () => {
  return {
    fontSize: getIOSFontSize(),
    WebkitAppearance: "none" as const,
    borderRadius: "8px",
    WebkitTransform: "translateZ(0)",
    transform: "translateZ(0)",
  };
};

/**
 * Enhanced hook for managing iOS textarea focus/blur events with keyboard detection
 */
export const useIOSTextareaFix = () => {
  let viewportState: IOSViewportState = { originalViewport: "", originalScrollY: 0 };
  let keyboardHeight = 0;

  const handleFocus = () => {
    viewportState = handleIOSTextareaFocus();

    // Add a small delay to allow keyboard to appear and then adjust scrolling
    setTimeout(() => {
      const drawerBody = document.querySelector(".drawer-body");
      if (drawerBody && isIOS()) {
        // Ensure the textarea and content below it are accessible
        const textarea = document.querySelector(".ios-textarea-fix:focus");
        if (textarea) {
          // Scroll the textarea into view within the drawer
          textarea.scrollIntoView({
            behavior: "smooth",
            block: "center",
            inline: "nearest",
          });
        }
      }
    }, 300); // Wait for keyboard animation
  };

  const handleBlur = () => {
    handleIOSTextareaBlur(viewportState);
    keyboardHeight = 0;
  };

  const cleanup = () => {
    if (viewportState.originalViewport) {
      handleIOSTextareaBlur(viewportState);
    }
  };

  return { handleFocus, handleBlur, cleanup, isIOS: isIOS() };
};
