import type { NextApiRequest, NextApiResponse } from 'next';
import { getStripeInstance, type StripeCustomerRequest } from '@/lib/stripe';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { email, isUS }: StripeCustomerRequest = req.body;

    if (!email) {
      return res.status(400).json({ error: 'Email is required' });
    }

    const stripeService = getStripeInstance(isUS === 'true');

    console.log('[stripe/customer/retrieve] Routing', { region: isUS === 'true' ? 'us' : 'row', email });

    let customerData;

    // Gets the customer who's email id matches the one sent by the client
    const customerList = await stripeService.customers.list({
      email,
      limit: 1,
      expand: ['data.tax'],
    });

    // Checks if the customer exists, if not creates a new customer
    if (customerList.data.length !== 0) {
      customerData = customerList.data[0];
    } else {
      const customer = await stripeService.customers.create({
        email,
      });
      customerData = customer;
    }

    res.status(200).json({
      customer: customerData,
      success: true,
    });

  } catch (error) {
    console.error('Error retrieving/creating customer:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    res.status(500).json({
      success: false,
      error: errorMessage
    });
  }
}
