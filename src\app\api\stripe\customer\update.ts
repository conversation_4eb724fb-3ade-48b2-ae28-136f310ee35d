import type { NextApiRequest, NextApiResponse } from 'next';
import { getStripeInstance, type StripeUpdateCustomerRequest } from '@/lib/stripe';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const {
      customerId,
      paymentMethodId,
      city,
      country,
      line1,
      line2,
      postalCode,
      state,
      name,
      phone,
      isUS
    }: StripeUpdateCustomerRequest = req.body;

    if (!customerId) {
      return res.status(400).json({ error: 'Customer ID is required' });
    }

    const stripeService = getStripeInstance(isUS === 'true');

    console.log('[stripe/customer/update] Routing', { region: isUS === 'true' ? 'us' : 'row', customerId, paymentMethodId });


    const customer = await stripeService.customers.update(customerId, {
      invoice_settings: {
        default_payment_method: paymentMethodId,
      },
      address: {
        city,
        country,
        line1,
        line2,
        postal_code: postalCode,
        state
      },
      name,
      phone
    });

    res.status(200).json({
      customer,
      success: true,
    });

  } catch (error) {
    console.error('Error updating customer:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    res.status(500).json({
      success: false,
      error: errorMessage
    });
  }
}
