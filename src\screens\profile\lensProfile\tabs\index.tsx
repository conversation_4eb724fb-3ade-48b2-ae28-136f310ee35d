import { useEffect, useRef, useState } from "react";
import { TabsContent } from "@/components/ui/tabs";
import PostsMyProfile from "./posts";
import ServicesMyProfile from "./services";
import { EventsMyProfile } from "./events";
import { ProfileMyProfile } from "./profile";

import * as Tabs from "@radix-ui/react-tabs";

import { Button } from "@/components/ui/button";
import OtherServicesMProfile from "./otherServices";
import { Plus, X } from "react-feather";
import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogHeader,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import CreatePost from "./posts/createPost";
import CreateEvent from "./events/createEvent";
import CreateService from "./services/createService";
import CreateProfile from "./profile/createProfile";
import useAuth from "@/hook";
import useProfile from "@/hook/profileData";
import { themes } from "../../../../../theme";
import { FullAccountQuery } from "@/graphql/test/generated";
import { getId } from "@/services/authBridgeService";
type ProfileMyProfileProps = {
  activeBgColor?: string; // New prop for dynamic active border color
  isOtherProfile?: any;
  otherUserID?: any;
  isOtherProfileStatus: boolean;
  profileQueryData: FullAccountQuery | undefined;
  selectedTabs: any;
  selectedTabsStatus: any;
  setSelectedProfileTabs: any;
  selectedProfileTabs: any;
  setSelectedTabs: any;
};
export function TabsMyProfile({
  activeBgColor,
  isOtherProfile,
  otherUserID,
  isOtherProfileStatus,
  profileQueryData,
  selectedTabs,
  selectedTabsStatus,
  setSelectedProfileTabs,
  selectedProfileTabs,
  setSelectedTabs,
}: ProfileMyProfileProps) {
  const postsRef = useRef<HTMLDivElement>(null);

  const auth = useAuth();
  const [isbothID, setIsbothID] = useState(false);

  const [userId, setUserId] = useState("");
  const getLensUserId = async (otherUserIDs: any) => {
    try {
      const resp = await getId(
        { id: otherUserIDs }
        // { id: "0x08cfd6" }
      ); // send lens_code(web3) or user_id (web2)
      // console.log({ resp });
      if (resp) {
        // router.push(`profile/${resp.lens_code}`);
        setUserId(resp?.user_id);
      }

      if (resp?.user_id && resp.lens_id && resp.wallet_id) {
        // router.push(`profile/${resp.lens_code}`);
        setIsbothID(true);
      }
    } catch (error) {
      console.log({ error });
      setUserId("");
    }
  };

  useEffect(() => {
    // console.log(props?.otherUserID);

    getLensUserId(otherUserID);
  }, [otherUserID]);

  const profile = useProfile(userId);

  const [isProfile, setIsProfile] = useState(selectedTabs);

  // Extract post lengths safely

  const starredPostsLength = profile?.profileData?.starredPosts?.length || 0;

  const [isOpen, setIsOpen] = useState(false);
  const [activeTabs, setActiveTabs] = useState("Posts");
  useEffect(() => {
    setIsProfile(selectedTabs ? selectedTabs : "Posts");
    // alert("Click");
  }, [selectedTabsStatus]);

  return (
    <>
      <div className="relative container mx-auto w-full px-3 mt-2 overflow-hidden h-[calc(100vh-130px)] max-md:h-[calc(100vh-17px)] ">
        <Tabs.Root
          defaultValue="Posts"
          value={isProfile}
          className="border-0 overflow-scroll h-full hide-scroll"
        >
          <div className="sticky top-0 z-50 pb-2 flex flex-row items-center justify-center w-full bg-white">
            <Tabs.List
              className="TabsListBg w-[400px] max-md:w-full"
              aria-label="Manage your account"
              style={
                {
                  "--active-bg-color": activeBgColor,
                } as React.CSSProperties
              }
            >
              <Tabs.Trigger
                className={"TabsTriggerBg"}
                value="Posts"
                style={{}}
                onClick={() => {
                  setActiveTabs("Posts"), setIsProfile("Posts"), setSelectedTabs("Posts");
                }}
              >
                Posts
              </Tabs.Trigger>
              <Tabs.Trigger
                className={
                  isOtherProfile ? "TabsTriggerBg  cursor-not-allowed opacity-45" : "TabsTriggerBg"
                }
                value="Services"
                onClick={() => {
                  setActiveTabs("Services"), setIsProfile("Services"), setSelectedTabs("Services");
                }}
                disabled={isOtherProfile ? true : false}
              >
                Services
              </Tabs.Trigger>
              <Tabs.Trigger
                className={
                  isOtherProfile ? "TabsTriggerBg  cursor-not-allowed opacity-45" : "TabsTriggerBg"
                }
                value="Events"
                onClick={() => {
                  setActiveTabs("Events"), setIsProfile("Events"), setSelectedTabs("Events");
                }}
                disabled={isOtherProfile ? true : false}
              >
                Events
              </Tabs.Trigger>
              <Tabs.Trigger
                className="TabsTriggerBg"
                value="Profiles"
                onClick={() => {
                  setActiveTabs("Profiles"), setIsProfile("Profiles"), setSelectedTabs("Profiles");
                }}
              >
                Profiles
              </Tabs.Trigger>
            </Tabs.List>
          </div>

          <div className="pb-12">
            <TabsContent
              value="Posts"
              className="w-full"
              style={{ background: "white", width: "100%" }}
              ref={postsRef}
            >
              <div className="row justify-center  w-full bg-[#fafafa] sticky top-12 max-md:top-10 -mt-3 md:min-h-[70px]  z-[50]">
                <div className="row gap-5 max-md:gap-2 py-2 justify-between flex w-full px-4 max-md:px-1">
                  {/* { isOtherProfile && ( */}
                  <div className="row gap-3 items-center pt-1">
                    {profile?.profileData?.categories?.map((item: any, index: any) => (
                      <div key={index}>
                        {Object.entries(themes).map(([_, innerThemeProperties]) => (
                          <div key={innerThemeProperties.title}>
                            {(item === "Storytelling" ? "Literature" : item) ===
                              innerThemeProperties.title && (
                              <Button
                                variant="outline"
                                className="font-bold h-[30px] max-md:text-xs max-md:font-medium max-md:px-2"
                                style={{
                                  color: innerThemeProperties.backgroundColor,
                                  borderColor: innerThemeProperties.backgroundColor || "#333333",
                                }}
                              >
                                {item}
                              </Button>
                            )}
                          </div>
                        ))}
                      </div>
                    ))}
                  </div>
                  {/* )} */}
                  <div className="row gap-3 items-center ">
                    <div className="text-center">
                      <p className="small-heading ">
                        {profileQueryData?.accountStats?.feedStats?.posts}
                      </p>
                      <p className="small-subheading">Posts</p>
                    </div>

                    <div className="text-center">
                      <p className="small-heading ">{starredPostsLength}</p>
                      <p className="small-subheading">Starred</p>
                    </div>
                  </div>
                </div>
              </div>
              <PostsMyProfile
                borderColor={activeBgColor || "#333333"}
                isOtherProfile={isOtherProfile}
                otherUserID={otherUserID}
              />
            </TabsContent>
            <TabsContent value="Services" ref={postsRef}>
              <div className=" row justify-center  w-full bg-[#fafafa] sticky top-12 max-md:top-10 -mt-3 md:min-h-[70px]  z-[50]">
                <div className="row gap-5 max-md:gap-2 py-2 justify-between flex w-full px-4 max-md:px-1">
                  <div className="">
                    <p className="small-heading ">
                      {profile?.profileData?.languages && profile?.profileData?.languages.length > 0
                        ? profile.profileData.languages.length > 1
                          ? `${profile.profileData.languages[0]} +${profile.profileData.languages.length - 1}`
                          : profile.profileData.languages[0]
                        : "No Language"}
                    </p>
                    <p className="small-subheading">Language</p>
                  </div>
                  <div className="">
                    <p className="small-heading ">23</p>
                    <p className="text-subtitle1  max-md:text-sm -mt-2">Orders</p>
                  </div>
                  <div className="">
                    <p className="small-heading ">12</p>
                    <p className="small-subheading">Сustom</p>
                  </div>
                </div>
              </div>

              {true ? (
                <OtherServicesMProfile
                  activeColor={activeBgColor}
                  isOtherProfile={isOtherProfile}
                  otherUserID={userId}
                />
              ) : (
                <ServicesMyProfile activeColor={activeBgColor} />
              )}
            </TabsContent>
            <TabsContent value="Events" ref={postsRef}>
              <EventsMyProfile activeBorderColor={activeBgColor} otherUserID={userId} />
            </TabsContent>
            <TabsContent
              value="Profiles"
              ref={postsRef}
              style={{ background: "white", width: "100%" }}
            >
              <ProfileMyProfile
                activeBorderColor={activeBgColor}
                otherUserID={otherUserID}
                isOtherProfileStatus={isOtherProfileStatus}
                follower={profileQueryData?.accountStats?.graphFollowStats?.followers ?? 0}
                following={profileQueryData?.accountStats?.graphFollowStats?.following ?? 0}
                selectedProfileTabs={selectedProfileTabs}
              />
            </TabsContent>
          </div>
        </Tabs.Root>
      </div>

      <div>
        <AlertDialog open={isOpen} onOpenChange={setIsOpen}>
          <AlertDialogTrigger asChild>
            {/* Empty or hidden trigger since we're controlling externally */}
            <span style={{ display: "none" }}></span>
          </AlertDialogTrigger>
          <AlertDialogContent
            className="min-w-96  min-h-[80vh] p-6 rounded-xl max-md:w-full max-md:rounded-none max-md:h-full"
            // style={{ borderRadius: "20px" }}
          >
            <AlertDialogHeader>
              <AlertDialogDescription>
                <div className="row justify-between">
                  <div onClick={() => setIsOpen(false)} className=" cursor-pointer">
                    <X />
                  </div>
                  <p className="font-bold text-primary">
                    {activeTabs == "Posts" && "New Post"}
                    {activeTabs == "Services" && "Create Service"}
                    {activeTabs == "Events" && "Create Events"}
                    {activeTabs == "Profiles" && "Create Profile"}
                  </p>
                  <p className={false ? "font-bold text-primary" : "font-bold text-borderColor"}>
                    {/* Save */}
                  </p>
                </div>

                <div className="h-[80vh] max-md:h-[90vh] overflow-y-scroll hide-scroll-custom">
                  {activeTabs == "Posts" && <CreatePost />}
                  {activeTabs == "Services" && <CreateService />}
                  {activeTabs == "Events" && <CreateEvent />}
                  {activeTabs == "Profiles" && <CreateProfile />}
                </div>
              </AlertDialogDescription>
            </AlertDialogHeader>
          </AlertDialogContent>
        </AlertDialog>
      </div>
    </>
  );
}

export default TabsMyProfile;
