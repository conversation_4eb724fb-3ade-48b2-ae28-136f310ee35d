import { User } from "@/services/UserInterface";
import { GetFiltersInput, userFilterWrapper } from "@/services/usersServices";
import { FieldPath, FieldValue, getFirestore } from "firebase-admin/firestore";
import { FollowerHandlerManager } from "./follow-handlers";
import { SendMailHandler } from "./handlers";
import { NotificationHandlerManager } from "./notification-handlers";
import { NotificationEvents } from "@/services/notificationService";
import { PostsHandlerManager } from "./post-handler";
import { generateUsername } from "../utils";

export class UsersHandlerManager {
  private USERS_COLLECTION = "users";

  static instance: UsersHandlerManager | null = null;

  private constructor() {}

  static getInstance() {
    if (!this.instance) {
      this.instance = new UsersHandlerManager();
    }
    return this.instance;
  }
  private _sanitizeUserPayload(data: User) {
    const { stripe_id, email, basketOrdersCount, password, ...safeData } = data;

    return safeData;
  }
  private _sanitizeBasicUserPayload(data: User) {
    const { basketOrdersCount, password, ...safeData } = data;
    return safeData;
  }

  GetAllUsers = async ({
    filters,
    uid,
  }: {
    filters?: Omit<GetFiltersInput, "date_of_publishing">;
    uid?: string;
  }) => {
    try {
      const db = getFirestore();

      const snapshot = await db.collection("users").get();

      let usersList: any[] = snapshot.docs
        .map(
          (doc) =>
            ({
              id: doc.id,
              ...doc.data(),
            }) as User
        )
        .filter((user) => user.isDeleted !== true);

      usersList = usersList?.map((c) => {
        return this._sanitizeUserPayload(c);
      });

      if (filters) {
        usersList = await userFilterWrapper({ filters, users: usersList });
      }
      console.log({ uid });

      // ✅ Add is_followed_by_me field
      if (uid) {
        const myFollowers = await FollowerHandlerManager.getInstance()?.GetFollowingsByUserId(uid);

        const followedIds = myFollowers.map((f: any) => (typeof f === "string" ? f : f.id));
        console.log({ followedIds });

        usersList = usersList.map((u) => ({
          ...u,
          is_followed_by_me: followedIds.includes(u.id),
        }));
      } else {
        // if no uid passed, default false
        usersList = usersList.map((u) => ({
          ...u,
          is_followed_by_me: false,
        }));
      }

      return usersList;
    } catch (error) {
      console.error("Error fetching users:", error);
      return { success: false, error: "Server error" };
    }
  };

  DeleteUserDetails = async ({ user_id, comment }: { user_id: string; comment: string }) => {
    try {
      if (!user_id) {
        throw new Error("user_id is required");
      }

      // 🚀
      // validate if user has active service then cannt delete

      // Perform cascading updates (mark user + related docs deleted)
      const results = await Promise.allSettled([
        this.updateUserDeleteFlag({ user_id, flag: true, comment }),
        this.updatePostDeleteFlag({ user_id, flag: true }),
        this.updateEventsDeleteFlag({ user_id, flag: true }),
        this.updateServicesDeleteFlag({ user_id, flag: true }),
        // updateOrdersDeleteFlag({ user_id, flag: true }),
        // updateAuthBridgeDeleteFlag({ user_id }),
        // updateFollowDeleteFlag({ user_id }),
        // updateCommentsDeleteFlag({ user_id, flag: true }),
      ]);

      // Delete the Firebase Auth user using Admin SDK
      // await admin.auth().deleteUser(user_id);
      const db = getFirestore();

      const userRef = db.collection("users").doc(user_id);
      await userRef.delete();

      // Send notification email
      await SendMailHandler({
        payload: {
          to: "<EMAIL>",
        },
        type: "profile_deleted",
        mail_type: "profile_deleted",
        message: {
          profileId: user_id,
          reason: comment,
        },
      });

      return {
        success: true,
        message: "User deletion process completed",
        results,
      };
    } catch (error) {
      console.error("❌ deleteUserDetails failed:", error);
      throw new Error("delete user details failed");
    }
  };

  async updateUserDeleteFlag({
    user_id,
    flag,
    comment,
  }: {
    user_id: string;
    flag: boolean;
    comment: string;
  }) {
    try {
      const db = getFirestore();
      const oldRef = db.collection("users").doc(user_id);
      const snapshot = await oldRef.get();

      if (snapshot.exists) {
        const data = snapshot.data();

        const newRef = db.collection("deleted_users").doc(user_id);
        await newRef.set(
          {
            ...data,
            delete_reason: comment,
            user_id_rem: user_id,
            isDeleted: true,
          },
          { merge: true }
        );

        await oldRef.delete();
      }

      return { success: true };
    } catch (error) {
      console.error("❌ user_delete_flag_failed:", error);
      throw new Error("user_delete_flag_failed");
    }
  }

  /**
   * Move all posts of a user to deleted_posts and delete originals.
   */
  async updatePostDeleteFlag({ user_id, flag }: { user_id: string; flag: boolean }) {
    try {
      const db = getFirestore();
      const userRef = db.collection("users").doc(user_id);
      const userSnap = await userRef.get();

      if (!userSnap.exists) throw new Error("User not found");

      const userData = userSnap.data() || {};
      const postIds: string[] = userData.posts || [];

      if (postIds.length === 0) {
        return { success: true, message: "No posts to update" };
      }

      for (const postId of postIds) {
        const postRef = db.collection("posts").doc(postId);
        const postSnap = await postRef.get();

        if (!postSnap.exists) continue;

        const postData = postSnap.data();

        await db
          .collection("deleted_posts")
          .doc(postId)
          .set({
            ...postData,
            deleted: true,
            user_id_rem: user_id,
            post_id_rem: postId,
          });

        await postRef.delete();
      }

      return { success: true, updatedPosts: postIds };
    } catch (error) {
      console.error("❌ post_delete_flag_failed:", error);
      throw new Error("post_delete_flag_failed");
    }
  }

  /**
   * Move all events of a user to deleted_events and delete originals.
   */
  async updateEventsDeleteFlag({ user_id, flag }: { user_id: string; flag: boolean }) {
    try {
      const db = getFirestore();
      const userRef = db.collection("users").doc(user_id);
      const userSnap = await userRef.get();

      if (!userSnap.exists) throw new Error("User not found");

      const userData = userSnap.data() || {};
      const eventIds: string[] = userData.events || [];

      if (eventIds.length === 0) {
        return { success: true, message: "No events to update" };
      }

      for (const eventId of eventIds) {
        const eventRef = db.collection("events").doc(eventId);
        const eventSnap = await eventRef.get();

        if (!eventSnap.exists) continue;

        const eventData = eventSnap.data();

        await db
          .collection("deleted_events")
          .doc(eventId)
          .set({
            ...eventData,
            deleted: true,
            user_id_rem: user_id,
            event_id_rem: eventId,
          });

        await eventRef.delete();
      }

      return { success: true, updatedEvents: eventIds };
    } catch (error) {
      console.error("❌ event_delete_flag_failed:", error);
      throw new Error("event_delete_flag_failed");
    }
  }

  /**
   * Move all services of a user to deleted_services and delete originals.
   */
  async updateServicesDeleteFlag({ user_id, flag }: { user_id: string; flag: boolean }) {
    try {
      const db = getFirestore();
      const userRef = db.collection("users").doc(user_id);
      const userSnap = await userRef.get();

      if (!userSnap.exists) throw new Error("User not found");

      const userData = userSnap.data() || {};
      const serviceIds: string[] = userData.services || [];

      // reset services list
      await userRef.update({ services: [] });

      if (serviceIds.length === 0) {
        return { success: true, message: "No services to update" };
      }

      for (const serviceId of serviceIds) {
        const serviceRef = db.collection("services").doc(serviceId);
        const serviceSnap = await serviceRef.get();

        if (!serviceSnap.exists) continue;

        const serviceData = serviceSnap.data();

        await db
          .collection("deleted_services")
          .doc(serviceId)
          .set({
            ...serviceData,
            deleted: true,
            user_id_rem: user_id,
            service_id_rem: serviceId,
          });

        await serviceRef.delete();
      }

      return { success: true, updatedServices: serviceIds };
    } catch (error) {
      console.error("❌ service_delete_flag_failed:", error);
      throw new Error("service_delete_flag_failed");
    }
  }

  async updateFollowDeleteFlag(user_id: string) {
    try {
      const db = getFirestore();
      const userRef = db.collection("users").doc(user_id);
      const userSnap = await userRef.get();

      if (!userSnap.exists) throw new Error("User not found");

      const userData = userSnap.data() || {};
      const followers: string[] = userData.followers || [];
      const following: string[] = userData.bookmarks || []; // assuming bookmarks = following

      const batch = db.batch();

      // Remove user_id from each follower's "following" array
      followers.forEach((followerId) => {
        const followerRef = db.collection("users").doc(followerId);
        batch.update(followerRef, {
          following: FieldValue.arrayRemove(user_id),
        });
      });

      // Remove user_id from each following user's "followers" array
      following.forEach((followingId) => {
        const followingRef = db.collection("users").doc(followingId);
        batch.update(followingRef, {
          followers: FieldValue.arrayRemove(user_id),
        });
      });

      await batch.commit();

      return {
        success: true,
        message: "User removed from all follow relationships",
      };
    } catch (error) {
      console.error("❌ follow_delete_failed:", error);
      throw new Error("follow_delete_failed");
    }
  }

  /**
   * Hide all comments made by a user (soft delete).
   */
  async updateCommentsDeleteFlag(user_id: string, flag: boolean) {
    try {
      const db = getFirestore();
      const commentsRef = db.collection("comments");
      const q = commentsRef.where("user_id", "==", user_id);
      const snapshot = await q.get();

      if (snapshot.empty) {
        return { success: true, message: "No comments found for user" };
      }

      const batch = db.batch();
      snapshot.forEach((docSnap) => {
        batch.update(docSnap.ref, { hidden: flag });
      });

      await batch.commit();

      return {
        message: "User deletion process completed",
      };
    } catch (error) {
      console.error("❌ comments_update_del_flag_failed:", error);
      throw new Error("comments_update_del_flag_failed");
    }
  }

  //

  GetFollowersIds = async (userId: string): Promise<string[]> => {
    try {
      const db = getFirestore();
      const followersSnap = await db
        .collection("users")
        .where("bookmarks", "array-contains", userId)
        .get();

      return followersSnap.docs.map((doc) => doc.id);
    } catch (error) {
      console.error("Error fetching followers:", error);
      return [];
    }
  };

  GetUserById = async (
    userId: string
  ): Promise<{
    success: boolean;
    user: User;
  }> => {
    try {
      if (!userId?.length) {
        return { success: false, user: {} as User };
      }
      const db = getFirestore();
      const userSnap = await db.collection("users").doc(userId).get();

      if (userSnap.exists) {
        const userData = userSnap.data() as User;

        // ⚠️ Can't use localStorage on server
        // You can handle currency persistence in another way if needed

        return {
          success: true,
          user: this._sanitizeBasicUserPayload({
            ...userData,
            following: userData.bookmarks || [],
            followers: (await this.GetFollowersIds(userId)) || [],
          }) as User,
        };
      } else {
        return { success: false, user: {} as User };
      }
    } catch (error) {
      console.error("Error fetching user:", error);
      return { success: false, user: {} as User };
    }
  };

  GetUserByEventId = async (eventId: string) => {
    try {
      const db = getFirestore();

      // Query users where "events" array contains eventId
      const querySnapshot = await db
        .collection("users")
        .where("events", "array-contains", eventId)
        .get();

      if (!querySnapshot.empty) {
        const users = querySnapshot.docs.map((doc) => {
          return this._sanitizeUserPayload({
            id: doc.id,
            ...doc.data(),
          } as User);
        });

        return { success: true, users };
      } else {
        return { success: false, error: "No users found for this event" };
      }
    } catch (error) {
      console.error("Error fetching users by eventId:", error);
      return { success: false, error: "Server error" };
    }
  };

  GetUserByServicesId = async (serviceId: string) => {
    try {
      const db = getFirestore();

      const querySnapshot = await db
        .collection("users")
        .where("services", "array-contains", serviceId)
        .get();

      if (!querySnapshot.empty) {
        const users = querySnapshot.docs.map((doc) => {
          return this._sanitizeUserPayload({
            id: doc.id,
            ...doc.data(),
          } as User);
        });

        return { success: true, users };
      } else {
        return { success: false, error: "No users found for this service" };
      }
    } catch (error) {
      console.error("Error fetching users by serviceId:", error);
      return { success: false, error: "Server error" };
    }
  };

  // lens
  // filter names

  GetUsersByCategory = async (
    {category , currentUserId , filter}:{
category: any ,
    currentUserId?:string,
     filter?:{
  lens_user?: string[];
  amuzn_user?: string[];
}      
    }
    
    ) => {
    try {
      const db = getFirestore();

  if (category === "my-feed") {
        if (!currentUserId) {
          throw new Error("currentUserId required for my-feed");
        }

        // get current user’s bookmarks
        const currentUserRef = db.doc(`users/${currentUserId}`);
        const currentUserSnap = await currentUserRef.get();
        const currentUserData = currentUserSnap.data();
        console.log({currentUserData});
        

        const bookmarks: string[] = currentUserData?.bookmarks ?? [];

        if (bookmarks.length > 0) {
          const bookmarkedUsersSnap = await db
            .collection("users")
            .where(FieldPath.documentId(), "in", bookmarks)
            .get();

         let users = bookmarkedUsersSnap.docs
            .map((doc) => {
              const userData = doc.data();
              const valid =
                // userData.posts &&
                // userData.posts.length > 0 &&
                userData?.isDeleted !== true;

              if (!valid) return null;

              if (filter?.amuzn_user?.length || filter?.lens_user?.length) {
                if (!filter?.amuzn_user?.includes(userData?.profile_name)) {
                  return null;
                }
              }

              return this._sanitizeUserPayload({
                id: doc.id,
                ...userData,
              } as User);
            })
            .filter((u) => u !== null);
 return {
          users,
          lens:[]
        }

        }
        return {
          users:[],
          lens:[]
        }
      }else{

      const querySnapshot = await db
        .collection("users")
        .where("categories", "array-contains", category)
        .get();

      const users = querySnapshot.docs
        .map((doc) => {
          const userData = doc.data();
          const valid =
            userData.posts &&
            userData.posts.length > 0 &&
            userData?.isDeleted !== true;

          if (!valid) return null;

          // apply amuzn_user filter if provided
          if(filter?.amuzn_user?.length || filter?.lens_user?.length) { 
          if (!filter?.amuzn_user?.includes(userData?.profile_name as string)) {
            return null;
          }
          }

          return this._sanitizeUserPayload({ id: doc.id, ...userData } as User);
        })
        .filter((u) => u !== null);

     const lensQuerySnapshot = await db
        .collection("lens")
        .where("category", "==", (category as string)?.toLowerCase())
        .get();
        console.log({lensQuerySnapshot:lensQuerySnapshot?.docs});
        
      let lens = lensQuerySnapshot.docs
        .map((doc) => {
          const lensData = doc.data();
          return { id: doc.id, ...lensData } ;
        })
        .filter((l) => l !== null);

        let _lens:string[] = [];
        
        lens = (lens?.[0]?.lens_profiles ?? [])?.map((val)=>{
          if(filter?.amuzn_user?.length || filter?.lens_user?.length) { 
          if(filter?.lens_user?.length > 0 && filter?.lens_user?.includes(val)) { 
            _lens.push(val);
          }else{
            return;
          }
          }
          
          if(!filter?.lens_user?.length && !filter?.amuzn_user?.length){
            _lens.push(val);
          }
        });

        console.log({after:_lens});

        
        
    return { users, lens:_lens?.map((c)=>{
      return {
        profile_name:c,
        category
      }
    }) };
      }

    } catch (error) {
      console.error("Error fetching users by category:", error);
      return [];
    }
  };

  GetUsersByCategoryWithPost = async (
    categories: string[],
    filters?: Omit<GetFiltersInput, "date_of_publishing">
  ) => {
    try {
      const db = getFirestore();

      // Query users whose categories contain ANY of the given categories
      const querySnapshot = await db
        .collection("users")
        .where("categories", "array-contains-any", categories)
        .get();

      const usersWithPosts = await Promise.all(
        querySnapshot.docs.map(async (userDoc) => {
          const userData = userDoc.data();

          if (!userData.posts || userData.posts.length === 0) {
            return { id: userDoc.id, ...userData, posts: [] };
          }

          // Fetch post details
          const postPromises = userData.posts.map(async (postId: string) => {
            const postSnapshot = await db.collection("posts").doc(postId).get();
            if (!postSnapshot.exists) return null;

            const postData = postSnapshot.data();
            return { id: postSnapshot.id, ...postData };
          });

          let posts = (await Promise.all(postPromises)).filter(
            (post: any) => post !== null && categories.includes(post.category)
          );

          // Sort posts by added_at.seconds descending
          posts = posts.sort(
            (a: any, b: any) => (b.added_at?.seconds || 0) - (a.added_at?.seconds || 0)
          );

          return posts.length > 0 ? { id: userDoc.id, ...userData, posts } : null;
        })
      );

      let resp: any = usersWithPosts.filter((user) => user !== null);

      if (filters) {
        resp = await userFilterWrapper({ filters, users: resp });
      }

      return resp?.map((c: any) => this._sanitizeUserPayload(c as User));
    } catch (error) {
      console.error("Error fetching users and posts:", error);
      return [];
    }
  };

  checkUserNameExists = async (userName: string): Promise<boolean> => {
    const db = getFirestore();

    const snapshot = await db
      .collection("users")
      .where("profile_name", "==", userName)
      .limit(1)
      .get();

    return !snapshot.empty;
  };

  GetUserIdByProfileName = async (profile_name: string): Promise<string | null> => {
    try {
      const db = getFirestore();

      const querySnapshot = await db
        .collection("users")
        .where("profile_name", "==", profile_name)
        .limit(1) // optimization: only need one
        .get();

      if (querySnapshot.empty) {
        console.warn(`No user found with profile_name = ${profile_name}`);
        return null;
      }

      const userDoc = querySnapshot.docs[0];
      const userData = userDoc.data();

      return (userData.id as string) ?? userDoc.id;
    } catch (error) {
      console.error("Error fetching user by profile_name:", error);
      return null;
    }
  };

  GetProfileNameByUserId = async (user_id: string): Promise<string | null> => {
    try {
      const db = getFirestore();

      const querySnapshot = await db
        .collection("users")
        .where("id", "==", user_id)
        .limit(1) // optimization: only need one doc
        .get();

      if (querySnapshot.empty) {
        console.warn(`No user found with id = ${user_id}`);
        return null;
      }

      const userDoc = querySnapshot.docs[0];
      const userData = userDoc.data();

      return (userData.profile_name as string) ?? null;
    } catch (error) {
      console.error("Error fetching user by id:", error);
      return null;
    }
  };

  CheckUserStripeId = async (
    userId: string
  ): Promise<{
    success: boolean;
    hasStripeId: boolean;
    stripeId?: string;
    user?: User;
    error?: string;
  }> => {
    try {
      const db = getFirestore();
      const userSnap = await db.collection("users").doc(userId).get();

      if (!userSnap.exists) {
        return {
          success: false,
          hasStripeId: false,
          error: "User not found",
        };
      }

      const userData = userSnap.data() as User;
      const hasStripeId = Boolean(userData.stripe_id);

      return {
        success: true,
        hasStripeId,
        stripeId: userData.stripe_id,
        user: this._sanitizeBasicUserPayload(userData) as User,
      };
    } catch (error) {
      console.error("Error checking user Stripe ID:", error);
      return {
        success: false,
        hasStripeId: false,
        error: "Failed to check Stripe ID",
      };
    }
  };

  GetUserStripeId = async (uid: string): Promise<string | null> => {
    try {
      const db = getFirestore();
      const userSnap = await db.collection("users").doc(uid).get();

      if (!userSnap.exists) {
        return null;
      }

      const userData = userSnap.data() ?? {};
      return (userData.stripe_id as string) ?? null;
    } catch (error) {
      console.error("GetUserStripeId failed:", error);
      throw new Error("GetUserStripeId failed");
    }
  };
  UpdateUser = async (userId: string | undefined, updatedData: any) => {
    try {
      if (!userId) {
        console.error("Error updating user: userId is undefined");
        return { success: false, error: "User ID is required" };
      }

      const db = getFirestore();
      const userRef = db.collection("users").doc(userId);
      const userSnap = await userRef.get();

      if (!userSnap.exists) {
        return { success: false, error: "User not found" };
      }

      const current_profile_name = userSnap.data()?.profile_name;
      console.log({ userId, updatedData, current_profile_name });

      // If profile_name is being updated, check for duplicates
      if (Object.keys(updatedData).includes("profile_name") && updatedData?.["profile_name"]) {
        const snapshot = await db
          .collection("users")
          .where("profile_name", "==", updatedData["profile_name"])
          .limit(1)
          .get();

        const existingUser = snapshot.docs[0]?.data();

        if (existingUser?.id && existingUser.id !== userId) {
          return { success: false, error: "user name already exists!" };
        }
      }

      await userRef.update(updatedData);
      return { success: true };
    } catch (error) {
      console.error("Error updating user:", error);
      return { success: false, error: "Server error" };
    }
  };

  ToggleStarredPost = async (
    userId: string,
    postId: string,
    isStarred: boolean,
    actingUserId: string
  ) => {
    try {
      const db = getFirestore();

      const userRef = db.collection("users").doc(userId);

      await userRef.update({
        starredPosts: isStarred ? FieldValue.arrayRemove(postId) : FieldValue.arrayUnion(postId),
      });

      // Notification
      if (!isStarred || actingUserId !== userId) {
        const post_details = await PostsHandlerManager.getInstance().GetPostDetailsByPostId({
          post_id: postId,
        });

        if (!post_details?.user_id || actingUserId === post_details.user_id) {
          return { success: true };
        }

        NotificationHandlerManager.getInstance().CreateNotification({
          payload: {
            src_id: actingUserId,
            dest_id: post_details.user_id,
            event: NotificationEvents.REACTION,
            post_id: postId,
            post_url: post_details?.postFile,
            thumbnail_url: post_details?.thumbnailUrl,
          },
        });
      }

      return { success: true };
    } catch (error) {
      console.error("Error toggling starred post:", error);
      return { success: false, error: "Server error" };
    }
  };

  ToggleBookMarks = async (
    userId: string,
    postId: string,
    isStarred: boolean,
    actingUserId: string
  ) => {
    try {
      const db = getFirestore();

      const userRef = db.collection("users").doc(userId);

      await userRef.update({
        post_bookmarked: isStarred ? FieldValue.arrayRemove(postId) : FieldValue.arrayUnion(postId),
      });

      // Send notification only when adding a bookmark
      if (!isStarred) {
        const post_details = await PostsHandlerManager.getInstance().GetPostDetailsByPostId({
          post_id: postId,
        });
        console.log({ post_details });

        if (!post_details?.user_id || actingUserId === post_details.user_id) {
          return { success: true };
        }

        NotificationHandlerManager.getInstance().CreateNotification({
          payload: {
            src_id: actingUserId,
            dest_id: post_details.user_id,
            event: NotificationEvents.BOOKMARK,
            post_id: postId,
            post_url: post_details?.postFile,
            thumbnail_url: post_details?.thumbnailUrl,
          },
        });
      }

      return { success: true };
    } catch (error) {
      console.error("Error toggling bookmark:", error);
      return { success: false, error: "Server error" };
    }
  };

  UpdateSocials = async ({
    user_id,
    type,
    url,
  }: {
    user_id: string;
    type: "facebookLink" | "instagramLink" | "twitterLink" | "websiteLink" | "youtubeLink";
    url: string;
  }) => {
    try {
      if (!url || !/^https?:\/\/\S+$/i.test(url)) {
        throw new Error("Invalid URL");
      }
      if (!user_id?.length) {
        throw new Error("invalid user_id");
      }
      const db = getFirestore();
      console.log({ user_id });

      const userRef = db.collection("users").doc(user_id);

      await userRef.update({ [type]: url });

      return "success";
    } catch (error) {
      console.error("UpdateSocials error:", error);
      throw error;
    }
  };
  CalculateProfileComplete = async ({ user_id }: { user_id: string }) => {
    try {
      // scoring system:
      // profile_name 10%
      // about_me 10%
      // personal_moto 10%
      // avatar 20%
      // categories 30% (if any categories exist)
      // posts 20% (if any posts exist)

      const db = getFirestore();
      const userRef = db.collection("users").doc(user_id);
      const userSnap = await userRef.get();

      if (!userSnap.exists) {
        throw new Error("User not found");
      }

      const userData = userSnap.data();
      let completionPercentage = 0;

      if (userData?.profile_name) completionPercentage += 10;
      if (userData?.about_me) completionPercentage += 10;
      if (userData?.personal_moto) completionPercentage += 10;
      if (userData?.avatar) completionPercentage += 20;
      if (Array.isArray(userData?.categories) && userData.categories.length > 0)
        completionPercentage += 30;
      if (Array.isArray(userData?.posts) && userData.posts.length > 0) completionPercentage += 20;

      return { success: true, completionPercentage };
    } catch (error) {
      console.error("calculateProfileComplete error:", error);
      return {
        success: false,
        completionPercentage: 0,
        error: error instanceof Error ? error.message : "Unknown error",
      };
    }
  };

  GetUniqueUserName = async (user_name?: string): Promise<string> => {
    // if a preferred username was provided, check if it’s free
    if (user_name) {
      const exists = await this.checkUserNameExists(user_name);
      if (!exists) return user_name;
    }

    let uniqueName: string;
    let attempts = 0;

    // keep generating until we find a free one
    do {
      uniqueName = generateUsername();
      attempts++;
      if (attempts > 10) {
        throw new Error("Too many attempts to generate a unique username");
      }
    } while (await this.checkUserNameExists(uniqueName));

    return uniqueName;
  };

  ChangeIsUsFlag = async ({
    user_id,
    isUs
  }:{
    user_id:string;
    isUs:boolean;
  }) => {
    try {
      const db = getFirestore();
          // Reference to the user document
    const userRef = db.collection("users").doc(user_id);

    // Update the `isUs` field
    await userRef.update({ isUS:isUs });

    return { success: true };
    } catch (error) {
      throw new Error("erro in change isus flag");
    }
  }

  GetUsersByIds = async (
    userIds: string[],
    filters?: Omit<GetFiltersInput, "date_of_publishing">
  ): Promise<{
    success: boolean;
    users: Array<any>;
  }> => {
    try {
      const db = getFirestore();
      let usersData: any[] = [];

      for (const userId of userIds) {
        const userRef = db.collection("users").doc(userId);
        const userSnap = await userRef.get();

        if (!userSnap.exists) continue;

        const userData = userSnap.data() as User;

        let posts: any[] = [];
        if (Array.isArray(userData?.posts) && userData.posts.length > 0) {
          const postPromises = userData.posts.map(async (postId: string) => {
            const postRef = db.collection("posts").doc(postId);
            const postSnap = await postRef.get();
            return postSnap.exists ? { id: postSnap.id, ...postSnap.data() } : null;
          });

          posts = (await Promise.all(postPromises)).filter(Boolean);
        }

        usersData.push({
          ...userData,
          id: userSnap.id,
          posts,
          // following: userData.bookmarks || [],
          // followers: (await getFollowersIds(userId)) || [],
        });
      }

      if (filters) {
        usersData = await userFilterWrapper({ filters, users: usersData });
      }

      return { success: true, users: usersData?.map((c) => this._sanitizeUserPayload(c as User)) };
    } catch (error) {
      console.error("Error fetching users:", error);
      return { success: false, users: [] };
    }
  };
  async CheckSellerIsUS(sellerId: string): Promise<boolean> {
  try {
      const db = getFirestore();
    const sellerRef = db.doc(`users/${sellerId}`);
    const sellerSnap = await sellerRef.get();

    return Boolean(sellerSnap.exists && sellerSnap.data()?.isUS);
  } catch (e) {
    console.log(
      "Could not resolve seller isUS from Firestore; using currency fallback if provided."
    );
    return false;
  }
}
}
