import type { NextApiRequest, NextApiResponse } from 'next';
import { getStripeInstance, type StripePaymentMethodRequest } from '@/lib/stripe';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const {
      paymentMethodId,
      email,
      name,
      phone,
      city,
      country,
      line1,
      line2,
      postalCode,
      state,
      isUS
    }: StripePaymentMethodRequest = req.body;

    if (!paymentMethodId) {
      return res.status(400).json({ error: 'Payment method ID is required' });
    }

    const stripeService = getStripeInstance(isUS === 'true');

    console.log('[stripe/payment-method/update] Routing', { region: isUS === 'true' ? 'us' : 'row', paymentMethodId, email });


    const paymentMethod = await stripeService.paymentMethods.update(paymentMethodId, {
      billing_details: {
        email,
        name,
        phone,
        address: {
          city,
          country,
          line1,
          line2,
          postal_code: postalCode,
          state
        },
      },
    });

    res.status(200).json({
      payment_method: paymentMethod,
      success: true,
    });

  } catch (error) {
    console.error('Error updating payment method:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    res.status(500).json({
      success: false,
      error: errorMessage
    });
  }
}
