"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";

type Props = {
  children: React.ReactNode;
};

export default function ProtectedRoute({ children }: Props) {
  const router = useRouter();
  const [checking, setChecking] = useState(true);

  const allowedEmails = ["<EMAIL>","<EMAIL>" ,"<EMAIL>"];

  useEffect(() => {
    try {
      const userData = localStorage.getItem("user");

      if (!userData) {
        router.replace("/");
        return;
      }

      const user = JSON.parse(userData);

      if (!user.email || !allowedEmails.includes(user.email)) {
        router.replace("/");
        return;
      }

      setChecking(false);
    } catch (error) {
      router.replace("/");
    }
  }, [router]);

  if (checking) router.replace("/");
  return <>{children}</>;
}
