import type { NextApiRequest, NextApiResponse } from 'next';
import { getStripeInstance, type StripeUpdateTransferRequest } from '@/lib/stripe';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const {
      transferId,
      orderId,
      description,
      isUS
    }: StripeUpdateTransferRequest = req.body;

    if (!transferId) {
      return res.status(400).json({ error: 'Transfer ID is required' });
    }

    const stripeService = getStripeInstance(isUS === 'true');

    console.log('[stripe/transfer/update] Routing', { region: isUS === 'true' ? 'us' : 'row', transferId, orderId });


    const transfer = await stripeService.transfers.update(transferId, {
      metadata: { order_id: orderId },
      description
    });

    res.status(200).json({
      transfer,
      success: true,
    });

  } catch (error) {
    console.error('Error updating transfer:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    res.status(500).json({
      success: false,
      error: errorMessage
    });
  }
}
