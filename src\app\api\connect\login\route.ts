import { NextRequest, NextResponse } from 'next/server';
import { Stripe } from 'stripe';
import { getUserIdFromRequest } from '@/lib/auth/serverAuth';
import { upsertStripeAccountsDoc, setUserStripeId, upsertSellerFromStripeAccount, StripeAccountLike } from '@/services/stripeConnectAdminService';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY as string);

export async function POST(req: NextRequest) {
  try {
    // Parse request body first (to avoid consuming it later in auth helper)
    let body: any = {};
    try {
      body = await req.json();
    } catch (e) {
      // Request may not have JSON body; continue with empty body
      console.warn('Body parse skipped or failed in /api/connect/login:', e);
    }
    const { accountId, email, businessName } = body || {};

    // Get user ID from request (headers or query). Fallback to body.userId
    let userId = await getUserIdFromRequest(req);
    if (!userId) {
      userId = body?.userId;
    }

    if (!userId) {
      return NextResponse.json(
        {
          error: 'Authentication required. Please log in to connect your Stripe account.',
          code: 'AUTH_REQUIRED'
        },
        { status: 401 }
      );
    }

    // Validate that either accountId or email is provided
    if (!accountId && !email) {
      return NextResponse.json(
        { error: 'Either Stripe Account ID or email is required to connect existing account.' },
        { status: 400 }
      );
    }

    console.log('Connecting existing Stripe account for user:', userId);

    let account;

    if (accountId) {
      // Method 1: Connect using existing account ID
      try {
        account = await stripe.accounts.retrieve(accountId);
        
        // Verify the account exists and is valid
        if (!account || account.object !== 'account') {
          return NextResponse.json(
            { error: 'Invalid account ID or account not found.' },
            { status: 404 }
          );
        }

        console.log('Found existing account:', account.id);
      } catch (error) {
        console.error('Error retrieving account by ID:', error);
        return NextResponse.json(
          { error: 'Account not found or invalid account ID.' },
          { status: 404 }
        );
      }
    } else if (email) {
      // Method 2: Search for account by email (limited capability)
      // Note: Stripe doesn't provide direct email search, so we'll create a new account
      // and let the user complete the connection process
      try {
        // Check if account with this email already exists by creating a new one
        // If it exists, Stripe will return an error with account details
        account = await stripe.accounts.create({
          type: 'express',
          email: email,
          business_profile: businessName ? { name: businessName } : undefined
        });

        console.log('Created new account for existing user:', account.id);
      } catch (error: any) {
        // If account already exists, Stripe might return specific error
        if (error.code === 'account_already_exists') {
          return NextResponse.json(
            { error: 'An account with this email already exists. Please provide the account ID instead.' },
            { status: 409 }
          );
        }
        
        console.error('Error creating account:', error);
        return NextResponse.json(
          { error: 'Failed to connect account. Please check your email or provide account ID.' },
          { status: 500 }
        );
      }
    }

    // Ensure account was successfully retrieved or created
    if (!account) {
      return NextResponse.json(
        { error: 'Failed to connect or retrieve Stripe account.' },
        { status: 500 }
      );
    }

    // Save using Firebase Admin SDK services to avoid client permission issues
    try {
      await setUserStripeId({ userId, stripeAccountId: account.id });

      await upsertStripeAccountsDoc({
        accountId: account.id,
        userId,
        account: account as StripeAccountLike,
      });

      // Optionally keep sellers collection in sync as well
      await upsertSellerFromStripeAccount({
        userId,
        account: account as StripeAccountLike,
      });

      console.log('Saved connected Stripe account via Admin SDK:', {
        userId,
        accountId: account.id,
        email: account.email || email || null,
      });
    } catch (firebaseError) {
      console.error('Error saving via Admin SDK:', firebaseError);
      return NextResponse.json(
        { error: 'Account connected but failed to save to database. Please try again.' },
        { status: 500 }
      );
    }

    // Create account link for any additional setup if needed
    const origin = req.headers.get('origin') || 'http://localhost:3000';
    let accountLinkUrl = null;

    if (!account.details_submitted) {
      try {
        const accountLink = await stripe.accountLinks.create({
          account: account.id,
          refresh_url: `${origin}/`,
          return_url: `${origin}/`,
          type: 'account_onboarding',
        });
        accountLinkUrl = accountLink.url;
      } catch (linkError) {
        console.error('Error creating account link:', linkError);
        // Continue without link - account is still connected
      }
    }

    return NextResponse.json({
      success: true,
      message: 'Account connected successfully',
      accountId: account.id,
      userId,
      account: {
        id: account.id,
        email: account.email,
        businessName: account.business_profile?.name,
        detailsSubmitted: account.details_submitted,
        chargesEnabled: account.charges_enabled,
        payoutsEnabled: account.payouts_enabled,
        country: account.country,
        currency: account.default_currency
      },
      onboardingUrl: accountLinkUrl
    });
  } catch (error) {
    console.error('Error connecting Stripe account:', error);
    return NextResponse.json(
      { error: 'Failed to connect account' },
      { status: 500 }
    );
  }
}
