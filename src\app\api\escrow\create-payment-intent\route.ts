import { NextRequest, NextResponse } from 'next/server';
import { Stripe } from 'stripe';
import { createTransaction } from '@/services/transactionService';
import { getOrderById } from '@/services/ordersServices';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY as string);



export async function POST(request: NextRequest) {
  try {
    const {
      amount,
      currency,
      productName = 'Nascent Ventures',
      userId,
      sellerId,
      orderId,
      userEmail,
      userName,
      paymentMethodType = 'card'
    } = await request.json();

    if (!amount || !userId || !sellerId || !orderId) {
      return NextResponse.json({
        error: 'Amount, userId, sellerId, and orderId are required for escrow'
      }, { status: 400 });
    }

    if (!currency || typeof currency !== 'string') {
      return NextResponse.json({ error: 'Currency is required' }, { status: 400 });
    }

    // Optionally fetch order uniqueId for metadata/description
    let orderUniqueId: string | undefined;
    try {
      const orderRes = await getOrderById(orderId);
      if (orderRes.success && (orderRes as any).order?.uniqueId) {
        orderUniqueId = (orderRes as any).order.uniqueId as string;
      }
    } catch {}

    // Create transaction record
    const transactionResult = await createTransaction({
      userId,
      sellerId,
      orderId,
      amount: parseInt(amount.toString()),
      currency: String(currency).toLowerCase(),
      productName,
      userEmail: userEmail || '',
      isEscrow: true,
      status: 'pending'
    });

    if (!transactionResult.success) {
      return NextResponse.json({ error: 'Failed to create transaction record' }, { status: 500 });
    }

    const paymentIntent = await stripe.paymentIntents.create({
      amount: parseInt(amount.toString()),
      currency: String(currency).toLowerCase(),
      automatic_payment_methods: {
        enabled: true,
        allow_redirects: 'never',
      },
      capture_method: 'manual', // Manual capture for escrow
      metadata: {
        userId,
        sellerId,
        orderId,
        ...(orderUniqueId ? { orderUniqueId } : {}),
        productName,
        transactionId: transactionResult.transactionId || '',
        isEscrow: 'true',
        escrowStage: 'authorized',
        ...(userEmail && { userEmail }),
        ...(userName && { userName }),
        paymentMethodType: paymentMethodType || 'card',
      },
      description: `Payment to Nascent Ventures - Order ${orderUniqueId || orderId}`,
      receipt_email: userEmail || undefined,
    });

    return NextResponse.json({
      success: true,
      clientSecret: paymentIntent.client_secret,
      paymentIntentId: paymentIntent.id,
      transactionId: transactionResult.transactionId,
    });
  } catch (error) {
    console.error('Error creating escrow payment intent:', error);
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to create escrow payment intent'
      },
      { status: 500 }
    );
  }
}