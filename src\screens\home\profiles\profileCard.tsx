import { useCallback, useEffect, useRef, useState, useMemo } from "react";
import { getUsersByCategory } from "@/services/usersServices";
import { themes } from "../../../../theme";
import GlobalProfileCard from "@/globalComponents/globalProfileCard";
import useAuth from "@/hook";
import useProfile from "@/hook/profileData";

import { useFilter } from "@/context/FilterContext";
import GlobalProfileCardLens from "@/globalComponents/globalProfileCardLens";
import { sortProfiles } from "@/lib/helper";
import { FollowerManager } from "@/services/followServices";
import ProfileCardSkeleton from "@/components/CardSkeleton/ProfileCardSkeleton";
import {
  AccountsBulkQuery,
  FollowingOrderBy,
  PageSize,
  useAccountsBulkQuery,
  useFollowingQuery,
} from "@/graphql/test/generated";
import { getLocation } from "@/lib/utils";
import { getId } from "@/services/authBridgeService";
import { useAccount } from "wagmi";

const ProfileCard = (props: any) => {
  const user = useAuth();
  const { filters } = useFilter();

  console.log("ProfileCard filters:", filters);
  console.log("ProfileCard props.lensUsernames:", props.lensUsernames);
  console.log("ProfileCard category:", props.themeProperties?.title);

  const { profileData } = useProfile(user?.userId || "");

  // State management
  const [categoryData, setCategoryData] = useState<Record<string, any[]>>({});
  const [lensProfiles, setLensProfiles] = useState<any[]>([]);
  const [profilesData, setProfilesData] = useState<AccountsBulkQuery["accountsBulk"]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [filterUpdateTrigger, setFilterUpdateTrigger] = useState<number>(0);
  const [userId, setUserId] = useState("");
  const [profilesmy, setProfilesMy] = useState<Array<{ localName: string }>>([]);
  const [allFollowing, setAllFollowing] = useState<Array<{ localName: any }>>([]);

  // Memoized constants
  const categories = useMemo(
    () => [
      "Music",
      "Literature",
      "Art",
      "Film & Photography",
      "Theatre & Performance",
      "Multidisciplinary",
      "Groups",
      "Storytelling",
    ],
    []
  );

  // Create stable references for filter arrays to prevent unnecessary re-renders
  const stableAmuznFilter = useMemo(
    () => filters?.amuznProfilesFilter,
    [filters?.amuznProfilesFilter]
  );
  const stableLensFilter = useMemo(
    () => filters?.lensProfilesFilter,
    [filters?.lensProfilesFilter]
  );

  // Watch for filter changes and immediately clear stale data
  useEffect(() => {
    console.log("ProfileCard: Filter change detected, clearing stale data");
    setCategoryData({});
    setLensProfiles([]);
    setProfilesData([]);
    setFilterUpdateTrigger((prev) => prev + 1);
  }, [stableAmuznFilter, stableLensFilter]);

  // Direct useEffect without useCallback to avoid infinite loops
  useEffect(() => {
    // Don't fetch if we don't have a valid category
    if (!props.themeProperties?.title) {
      console.log("ProfileCard: No category provided, skipping fetch");
      setLoading(false);
      return;
    }

    const fetchAllUsersByCategory = async () => {
      try {
        setLoading(true);

        // Clear existing data immediately when filters change
        setCategoryData({});
        setLensProfiles([]);
        setProfilesData([]);
        setFilterUpdateTrigger((prev) => prev + 1); // Force re-render

        console.log("ProfileCard: Starting fetch with filters:", {
          category: props.themeProperties.title,
          userId: user?.userId,
          isLogin: user.isLogin,
          amuznProfilesFilter: stableAmuznFilter,
          lensProfilesFilter: stableLensFilter,
        });

        const response = await getUsersByCategory({
          category:
            props.themeProperties.title === "My Feed" ? "my-feed" : props.themeProperties.title,
          currentUserId: user?.userId,
          filter: {
            amuzn_user: stableAmuznFilter,
            lens_user: stableLensFilter,
          },
        });

        console.log("ProfileCard: getUsersByCategory response:", response);

        if (response?.users || response?.lens) {
          const categorizedData: Record<string, any[]> = {};
          let myFeed: any[] = [];

          // Process amuzn users if they exist
          if (Array.isArray(response.users) && response.users.length > 0) {
            // Process categories efficiently
            for (const category of categories) {
              const filteredPosts = response.users.filter((post: any) => {
                const userCategory = post?.categories?.[0];
                if (!post.profile_name) return false;

                return category === "Literature"
                  ? userCategory === category || userCategory === "Storytelling"
                  : userCategory === category;
              });

              categorizedData[category] = filteredPosts;
            }

            // Fetch following data only once if user is logged in
            if (user.isLogin && user.userId && props.themeProperties.title === "My Feed") {
              try {
                myFeed = await FollowerManager.getInstance().GetFollowingsByUserId(user.userId);
                categorizedData["My Feed"] = myFeed;
              } catch (error) {
                console.error("Error fetching following data:", error);
              }
            }
          }

          // Update states synchronously
          setCategoryData(categorizedData);
          setLensProfiles(response.lens || []);

          console.log("ProfileCard: Data set successfully", {
            categorizedData,
            lensProfiles: response.lens || [],
            currentCategory: props.themeProperties.title,
          });
        } else {
          // Clear data when no results
          setCategoryData({});
          setLensProfiles([]);
          console.log("ProfileCard: No data found, clearing state");
        }
      } catch (error) {
        console.error("ProfileCard: Error fetching posts:", error);
        setCategoryData({});
        setLensProfiles([]);
      } finally {
        setLoading(false);
        console.log("ProfileCard: Loading set to false");
      }
    };

    fetchAllUsersByCategory();
  }, [
    props.themeProperties.title,
    user?.userId,
    user.isLogin,
    stableAmuznFilter,
    stableLensFilter,
    categories,
  ]);

  const { data: profileDataByLensId } = useAccountsBulkQuery(
    {
      request: {
        usernames:
          props.themeProperties.title === "My Feed"
            ? profilesmy
            : lensProfiles?.map((name: any) => ({ localName: name.profile_name })) || [],
      },
    },
    {
      refetchOnWindowFocus: false,
      enabled:
        props.themeProperties.title === "My Feed"
          ? profilesmy.length > 0
          : lensProfiles?.length > 0,
      // Add key to force refetch when filters change
      queryKey: [
        "accountsBulk",
        props.themeProperties.title,
        JSON.stringify(stableAmuznFilter),
        JSON.stringify(stableLensFilter),
        lensProfiles?.length || 0,
      ],
    }
  );

  useEffect(() => {
    if (profileDataByLensId) {
      setProfilesData(profileDataByLensId?.accountsBulk || []);
      console.log("ProfileCard: Lens profiles data updated:", profileDataByLensId?.accountsBulk);
    } else {
      // Clear lens profiles data when query is disabled or no data
      setProfilesData([]);
      console.log("ProfileCard: Lens profiles data cleared");
    }
  }, [profileDataByLensId, stableAmuznFilter, stableLensFilter]);

  console.log("props.lensUsernames", profileDataByLensId);

  // Optimized lens user ID fetching

  // Optimized lens user ID fetching with memoization
  const getLensUserId = useCallback(async () => {
    if (!user.userId) return;

    try {
      const resp = await getId({ id: user.userId });
      if (resp?.lens_code && resp.lens_code !== userId) {
        setUserId(resp.lens_code);
      }
    } catch (error) {
      console.error("Error fetching lens user ID:", error);
    }
  }, [user.userId, userId]);

  useEffect(() => {
    getLensUserId();
  }, [getLensUserId]);
  const { data: following } = useFollowingQuery(
    {
      request: {
        account: userId,
        pageSize: PageSize.Fifty,
        orderBy: FollowingOrderBy.Desc,
        cursor: currentFollowingCursor,
      },
    },
    {
      refetchOnWindowFocus: false,
      enabled: !!userId && props.themeProperties.title === "My Feed",
    }
  );

  interface FollowingItem {
    localName: any;
  }

  // Optimized following data processing
  useEffect(() => {
    if (
      following?.following?.items &&
      following.following.items.length > 0 &&
      props.themeProperties.title === "My Feed"
    ) {
      const newFollowing: FollowingItem[] = following.following.items.map((item: any) => ({
        localName: item.following.username?.localName,
      }));

      // Update allFollowing state efficiently
      setAllFollowing((prev) => {
        const combined = [...prev, ...newFollowing];
        // Remove duplicates based on localName
        const unique = combined.filter(
          (item, index, self) => index === self.findIndex((t) => t.localName === item.localName)
        );
        return unique;
      });
    }
  }, [following, props.themeProperties.title]);

  // Sync profilesmy with allFollowing
  useEffect(() => {
    if (allFollowing.length > 0) {
      setProfilesMy(allFollowing);
    }
  }, [allFollowing]);

  console.log("ProfileCard: Current state for merging:", {
    currentCategory: props.themeProperties.title,
    categoryData: categoryData,
    categoryDataForCurrentCategory: categoryData[props.themeProperties.title],
    lensProfiles: lensProfiles,
    profilesData: profilesData,
    stableAmuznFilter,
    stableLensFilter,
  });

  const amuznProfilesForCategory = categoryData[props.themeProperties.title] || [];
  const lensProfilesForCategory = profilesData || [];

  const mergedProfiles = useMemo(() => {
    console.log("ProfileCard: Recalculating mergedProfiles", {
      filterUpdateTrigger,
      amuznCount: amuznProfilesForCategory.length,
      lensCount: lensProfilesForCategory.length,
    });

    return sortProfiles(
      [
        ...amuznProfilesForCategory.map((item) => ({
          ...item,
          profile_name: item.profile_name || "Profile Name*",
          lensProfile: false, // Mark as amuzn profile
        })),
        ...lensProfilesForCategory.map((profile) => ({
          id: profile.username?.localName,
          profile_name: profile.metadata?.name || "Profile Name*",
          avatar: profile?.metadata?.picture || "",
          location: getLocation(profile.metadata?.attributes) || profile.username?.localName,
          isFollow: profile?.operations?.isFollowedByMe,
          lensProfile: true, // Mark lens profiles
        })),
      ],
      "profile_name"
    );
  }, [amuznProfilesForCategory, lensProfilesForCategory, filterUpdateTrigger]);

  console.log("ProfileCard: mergedProfiles result:", {
    amuznCount: amuznProfilesForCategory.length,
    lensCount: lensProfilesForCategory.length,
    totalMerged: mergedProfiles.length,
    mergedProfiles,
  });
  return (
    <>
      {loading ? (
        <div className="">
          <ProfileCardSkeleton count={6} columns={1} showGrid={true} />
        </div>
      ) : (
        <div className="w-full mt-0">
          {Object.entries(themes).map(([, themeProperties], indexM) => (
            <div key={indexM}>
              {props.themeProperties.title === themeProperties.title && (
                <div>
                  {(() => {
                    const currentCategory = themeProperties.title;

                    // Check if filters are applied and if current category is in selected categories
                    if (filters.categories && filters.categories.length > 0) {
                      if (!filters.categories.includes(currentCategory)) {
                        // Current category is not in selected filters, show no data
                        return (
                          <div className="w-full mt-2 p-4 bg-gray-50 rounded-md border border-gray-200 text-center">
                            <p className="text-gray-500">No profiles found in this category.</p>
                          </div>
                        );
                      }
                    }

                    return mergedProfiles.length > 0 ? (
                      mergedProfiles.map((post, index) => (
                        <div key={index} className="mt-0 w-[350px] max-w-[350px]">
                          {themeProperties.title === "My Feed" && !post.lensProfile ? (
                            Object.entries(themes).map(([_, innerThemeProperties], idx) => (
                              <div key={idx}>
                                {innerThemeProperties.title ===
                                  (post?.categories[0] === "Storytelling"
                                    ? "Literature"
                                    : post?.categories[0]) && (
                                  <GlobalProfileCard
                                    themeProperties={innerThemeProperties}
                                    isFollow={!profileData?.followers.includes(post.id)}
                                    location={post.location}
                                    profile_name={post.profile_name}
                                    avatar={post.avatar}
                                    id={post.id}
                                  />
                                  // <p>{post.profile_name}hii</p>
                                )}
                              </div>
                            ))
                          ) : themeProperties.title === "My Feed" && post.lensProfile ? (
                            <div>
                              <GlobalProfileCardLens
                                themeProperties={themeProperties}
                                isFollow={post.isFollow}
                                location={post.location}
                                profile_name={post.profile_name}
                                avatar={post.avatar}
                                id={post.id}
                              />
                            </div>
                          ) : post.lensProfile ? (
                            <GlobalProfileCardLens
                              themeProperties={themeProperties}
                              isFollow={post.isFollow}
                              location={post.location}
                              profile_name={post.profile_name}
                              avatar={post.avatar}
                              id={post.id}
                            />
                          ) : (
                            <GlobalProfileCard
                              themeProperties={themeProperties}
                              isFollow={!profileData?.followers.includes(post.id)}
                              location={post.location}
                              profile_name={post.profile_name}
                              avatar={post.avatar}
                              id={post.id}
                            />
                          )}
                        </div>
                      ))
                    ) : (
                      <p className="text-gray-500">No Profile available in this category.</p>
                    );
                  })()}
                </div>
              )}
            </div>
          ))}
        </div>
      )}
    </>
  );
};

export default ProfileCard;
