import { useCallback, useEffect, useRef, useState, useMemo } from "react";
import { getAllUsers } from "@/services/usersServices";
import { themes } from "../../../../theme";
import GlobalProfileCard from "@/globalComponents/globalProfileCard";
import useAuth from "@/hook";
import useProfile from "@/hook/profileData";
import { getLensProfilesById } from "@/services/lensService";
import { useFilter } from "@/context/FilterContext";
import GlobalProfileCardLens from "@/globalComponents/globalProfileCardLens";
import { sortProfiles } from "@/lib/helper";
import { FollowerManager } from "@/services/followServices";
import ProfileCardSkeleton from "@/components/CardSkeleton/ProfileCardSkeleton";
import {
  AccountsBulkQuery,
  FollowingOrderBy,
  PageSize,
  useAccountsBulkQuery,
  useFollowingQuery,
} from "@/graphql/test/generated";
import { getLocation } from "@/lib/utils";
import { getId } from "@/services/authBridgeService";
import { useAccount } from "wagmi";

const ProfileCard = (props: any) => {
  const user = useAuth();
  const { filters, getServiceFilters } = useFilter();

  console.log("ProfileCard filters:", filters);
  console.log("ProfileCard props.lensUsernames:", props.lensUsernames);
  console.log("ProfileCard category:", props.themeProperties?.title);

  const { profileData } = useProfile(user?.userId || "");

  // State management
  const [categoryData, setCategoryData] = useState<Record<string, any[]>>({});
  const [profilesData, setProfilesData] = useState<AccountsBulkQuery["accountsBulk"]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [userId, setUserId] = useState("");
  const [profiles, setProfiles] = useState<Array<{ localName: string }>>([]);
  const [profilesmy, setProfilesMy] = useState<Array<{ localName: string }>>([]);
  const [allFollowing, setAllFollowing] = useState<Array<{ localName: any }>>([]);

  // Refs for optimization
  const profilesRef = useRef<Array<{ localName: string }>>([]);
  const containerRef = useRef<HTMLDivElement>(null);
  const [currentFollowingCursor, setCurrentFollowingCursor] = useState<string | null>(null);

  // Memoized constants
  const categories = useMemo(
    () => [
      "Music",
      "Literature",
      "Art",
      "Film & Photography",
      "Theatre & Performance",
      "Multidisciplinary",
      "Groups",
      "Storytelling",
    ],
    []
  );

  // Memoized category for lens profiles
  const category = useMemo(
    () => props.themeProperties.title.toLowerCase(),
    [props.themeProperties.title]
  );

  // Optimized fetch function with memoized dependencies
  const fetchAllUsers = useCallback(async () => {
    try {
      setLoading(true);
      const serviceFilters = getServiceFilters();

      console.log("serviceFilters", serviceFilters);

      // Check if filters are applied
      const filtersApplied = Object.keys(serviceFilters).length > 0;

      // Check if we have meaningful filters beyond just user_id
      const hasValidUserIdFilter = serviceFilters.user_id && serviceFilters.user_id.length > 0;
      const hasOtherValidFilters =
        (serviceFilters.location && serviceFilters.location.length > 0) ||
        serviceFilters.date_of_publishing;

      // Also check the original filters object for profileName
      const hasProfileNameFilter = filters.profileName && filters.profileName.length > 0;

      // Conditional logic for getAllUsers call
      let response;
      if (filtersApplied) {
        // When filters are applied, call getAllUsers if:
        // 1. user_id exists and is not empty, OR
        // 2. there are other valid filters (location, date_of_publishing), OR
        // 3. there's a profileName filter in the original filters
        if (hasValidUserIdFilter) {
          // console.log("Calling getAllUsers with filters:", {
          //   hasValidUserIdFilter,
          //   hasOtherValidFilters,
          //   hasProfileNameFilter,
          //   serviceFilters,
          //   originalFilters: filters,
          // });
          response = await getAllUsers(serviceFilters);
        } else {
          // Skip API call if filters are applied but no meaningful filters exist
          // console.log("Skipping getAllUsers call - filters applied but no meaningful filters:", {
          //   filtersApplied,
          //   hasValidUserIdFilter,
          //   hasOtherValidFilters,
          //   hasProfileNameFilter,
          //   serviceFilters,
          //   originalFilters: filters,
          // });
          response = { users: [] };
        }
      } else {
        // When no filters are applied, always call getAllUsers normally
        console.log("Calling getAllUsers without filters");
        response = await getAllUsers(serviceFilters);
      }

      if (Array.isArray(response?.users) && response.users.length > 0) {
        const categorizedData: Record<string, any[]> = {};
        let myFeed: any[] = [];

        // Process categories efficiently
        for (const category of categories) {
          const filteredPosts = response.users.filter((post: any) => {
            const userCategory = post?.categories?.[0];
            if (!post.profile_name) return false;

            return category === "Literature"
              ? userCategory === category || userCategory === "Storytelling"
              : userCategory === category;
          });

          categorizedData[category] = filteredPosts;
        }

        // Fetch following data only once if user is logged in
        if (user.isLogin && user.userId) {
          try {
            myFeed = await FollowerManager.getInstance().GetFollowingsByUserId(user.userId);
            categorizedData["My Feed"] = myFeed;
          } catch (error) {
            console.error("Error fetching following data:", error);
          }
        }

        setCategoryData(categorizedData);
      }
    } catch (error) {
      console.error("Error fetching posts:", error);
    } finally {
      setLoading(false);
    }
  }, [user.userId, user.isLogin, getServiceFilters, categories]);

  // Debounced effect to prevent excessive API calls
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      fetchAllUsers();
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [fetchAllUsers]);

  // Optimized Lens profiles fetching with memoization
  useEffect(() => {
    const fetchLensProfilesByCategory = async () => {
      try {
        const resp = await getLensProfilesById(category);
        const lensProfiles: Array<{ localName: string }> =
          resp?.lens_ids?.map((curr: any) => ({
            localName: curr,
          })) || [];

        // Only update if profiles have actually changed
        if (JSON.stringify(profilesRef.current) !== JSON.stringify(lensProfiles)) {
          profilesRef.current = lensProfiles;
          setProfiles(lensProfiles);
        }
      } catch (error) {
        console.error("Error fetching lens profiles:", error);
      }
    };

    if (category) {
      fetchLensProfilesByCategory();
    }
  }, [category]);

  // Determine which usernames to use for the query
  const matchedProfile = filters.lensProfiles?.find(
    (p) => p.category.toLowerCase() === props.themeProperties.title?.toLowerCase()
  );

  const rawUsernames =
    props.themeProperties.title === "My Feed"
      ? profilesmy
      : filters.profileName && matchedProfile
        ? props.lensUsernames
        : // : filters.profileName &&
          //   filters.profileName.length > 0 &&
          //   props.lensUsernames &&
          //   props.lensUsernames.length > 0
          // ? props.lensUsernames
          !filters.profileName
          ? profiles || []
          : [];

  // Transform usernames to the correct format for the API
  const queryUsernames = rawUsernames?.map((username: any) => {
    if (typeof username === "string") {
      return { localName: username };
    } else if (username && typeof username === "object" && "localName" in username) {
      return { localName: username.localName };
    }
    return { localName: String(username) };
  });

  // console.log("ProfileCard queryUsernames:", queryUsernames);
  // console.log("ProfileCard query enabled check:", {
  //   isMyFeed: props.themeProperties.title == "My Feed",
  //   profilesmyLength: profilesmy.length,
  //   hasFilterProfileName: filters.profileName && filters.profileName.length > 0,
  //   hasLensUsernames: props.lensUsernames && props.lensUsernames.length > 0,
  //   profilesLength: profiles.length,
  // });

  const {
    data: profileDataByLensId,
    error: profileError,
    isLoading: loadingProfile,
  } = useAccountsBulkQuery(
    {
      request: {
        usernames:
          props.themeProperties.title === "My Feed"
            ? profilesmy
            : !filters.profileName
              ? profiles
              : filters.profileName &&
                  filters.profileName.length > 0 &&
                  props?.lensUsernames &&
                  props?.lensUsernames?.length > 0
                ? props?.lensUsernames?.map((name: string) => ({ localName: name }))
                : [{ localName: "" }],
      },
    },
    {
      refetchOnWindowFocus: false,
      enabled:
        props.themeProperties.title == "My Feed"
          ? profilesmy.length > 0
          : filters.profileName && filters.profileName.length > 0
            ? filters.profileName.length > 0
            : profiles.length > 0,
    }
  );

  useEffect(() => {
    if (profileDataByLensId) {
      console.log(
        "ProfileCard: Query executed successfully, data received:",
        profileDataByLensId?.accountsBulk
      );
      setProfilesData(profileDataByLensId?.accountsBulk);
    }
  }, [profileDataByLensId]);

  console.log("props.lensUsernames", profileDataByLensId);

  // Optimized lens user ID fetching

  // Optimized lens user ID fetching with memoization
  const getLensUserId = useCallback(async () => {
    if (!user.userId) return;

    try {
      const resp = await getId({ id: user.userId });
      if (resp?.lens_code && resp.lens_code !== userId) {
        setUserId(resp.lens_code);
      }
    } catch (error) {
      console.error("Error fetching lens user ID:", error);
    }
  }, [user.userId, userId]);

  useEffect(() => {
    getLensUserId();
  }, [getLensUserId]);
  const { data: following } = useFollowingQuery(
    {
      request: {
        account: userId,
        pageSize: PageSize.Fifty,
        orderBy: FollowingOrderBy.Desc,
        cursor: currentFollowingCursor,
      },
    },
    {
      refetchOnWindowFocus: false,
      enabled: !!userId && props.themeProperties.title === "My Feed",
    }
  );

  interface FollowingItem {
    localName: any;
  }

  // Optimized following data processing
  useEffect(() => {
    if (
      following?.following?.items &&
      following.following.items.length > 0 &&
      props.themeProperties.title === "My Feed"
    ) {
      const newFollowing: FollowingItem[] = following.following.items.map((item: any) => ({
        localName: item.following.username?.localName,
      }));

      // Update both states efficiently
      setAllFollowing((prev) => {
        const combined = [...prev, ...newFollowing];
        // Remove duplicates based on localName
        const unique = combined.filter(
          (item, index, self) => index === self.findIndex((t) => t.localName === item.localName)
        );
        return unique;
      });

      setProfiles((prev) => {
        const combined = [...prev, ...newFollowing];
        const unique = combined.filter(
          (item, index, self) => index === self.findIndex((t) => t.localName === item.localName)
        );
        return unique;
      });
    }
  }, [following, props.themeProperties.title]);

  // Sync profilesmy with allFollowing
  useEffect(() => {
    if (allFollowing.length > 0) {
      setProfilesMy(allFollowing);
    }
  }, [allFollowing]);

  // Memoized merged profiles to prevent unnecessary recalculations
  const mergedProfiles = useMemo(() => {
    const categoryProfiles = (categoryData[props.themeProperties.title] || []).map((item) => ({
      ...item,
      profile_name: item.profile_name || "Profile Name*",
    }));

    const lensProfiles = profilesData.map((profile) => ({
      id: profile.username?.localName,
      profile_name: profile.metadata?.name || "Profile Name*",
      avatar: profile?.metadata?.picture || "",
      location: getLocation(profile.metadata?.attributes) || profile.username?.localName,
      isFollow: profile?.operations?.isFollowedByMe,
      lensProfile: true,
    }));

    return sortProfiles([...categoryProfiles, ...lensProfiles], "profile_name");
  }, [categoryData, props.themeProperties.title, profilesData]);
  return (
    <>
      {loading ? (
        <div className="">
          <ProfileCardSkeleton count={6} columns={1} showGrid={true} />
        </div>
      ) : (
        <div className="w-full mt-0">
          {Object.entries(themes).map(([, themeProperties], indexM) => (
            <div key={indexM}>
              {props.themeProperties.title === themeProperties.title && (
                <div>
                  {(() => {
                    const currentCategory = themeProperties.title;

                    // Check if filters are applied and if current category is in selected categories
                    if (filters.categories && filters.categories.length > 0) {
                      if (!filters.categories.includes(currentCategory)) {
                        // Current category is not in selected filters, show no data
                        return (
                          <div className="w-full mt-2 p-4 bg-gray-50 rounded-md border border-gray-200 text-center">
                            <p className="text-gray-500">No profiles found in this category.</p>
                          </div>
                        );
                      }
                    }

                    return mergedProfiles.length > 0 ? (
                      mergedProfiles.map((post, index) => (
                        <div key={index} className="mt-0 w-[350px] max-w-[350px]">
                          {themeProperties.title === "My Feed" && !post.lensProfile ? (
                            Object.entries(themes).map(([_, innerThemeProperties], idx) => (
                              <div key={idx}>
                                {innerThemeProperties.title ===
                                  (post?.categories[0] === "Storytelling"
                                    ? "Literature"
                                    : post?.categories[0]) && (
                                  <GlobalProfileCard
                                    themeProperties={innerThemeProperties}
                                    isFollow={!profileData?.followers.includes(post.id)}
                                    location={post.location}
                                    profile_name={post.profile_name}
                                    avatar={post.avatar}
                                    id={post.id}
                                  />
                                  // <p>{post.profile_name}hii</p>
                                )}
                              </div>
                            ))
                          ) : themeProperties.title === "My Feed" && post.lensProfile ? (
                            <div>
                              <GlobalProfileCardLens
                                themeProperties={themeProperties}
                                isFollow={post.isFollow}
                                location={post.location}
                                profile_name={post.profile_name}
                                avatar={post.avatar}
                                id={post.id}
                              />
                            </div>
                          ) : post.lensProfile ? (
                            <GlobalProfileCardLens
                              themeProperties={themeProperties}
                              isFollow={post.isFollow}
                              location={post.location}
                              profile_name={post.profile_name}
                              avatar={post.avatar}
                              id={post.id}
                            />
                          ) : (
                            <GlobalProfileCard
                              themeProperties={themeProperties}
                              isFollow={!profileData?.followers.includes(post.id)}
                              location={post.location}
                              profile_name={post.profile_name}
                              avatar={post.avatar}
                              id={post.id}
                            />
                          )}
                        </div>
                      ))
                    ) : (
                      <p className="text-gray-500">No Profile available in this category.</p>
                    );
                  })()}
                </div>
              )}
            </div>
          ))}
        </div>
      )}
    </>
  );
};

export default ProfileCard;
