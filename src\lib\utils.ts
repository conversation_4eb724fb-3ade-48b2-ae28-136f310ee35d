import { clsx, type ClassValue } from "clsx";
import { getAuth, onAuthStateChanged } from "firebase/auth";
import { twMerge } from "tailwind-merge";
import { format } from "date-fns";
import { initFirebase } from "../../firebaseConfig";
import { getBlob, getDownloadURL, getStorage, ref } from "firebase/storage";
import { adjectivesBase, nounsBase } from "./constant";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}
export function getLocation(
  attributes: { type: string; key: string; value: string }[] | undefined
): string | undefined {
  const locationAttr = attributes?.find((attr) => attr.key === "location");
  return locationAttr?.value;
}

export function getFormattedTime(isoDate: string): string {
  const date = new Date(isoDate);
  const day = date.getUTCDate();
  const month = date.toLocaleString("default", { month: "long", timeZone: "UTC" });
  const year = date.getUTCFullYear();

  const formatted = `${day} ${month} ${year}`;
  return formatted;
}

export async function getIdToken() {
  const auth = getAuth();
  return new Promise((resolve, reject) => {
    onAuthStateChanged(auth, async (user) => {
      if (user) {
        const idToken = await user.getIdToken();
        resolve(idToken);
      } else {
        reject(new Error("User not logged in"));
      }
    });
  });
  //  const user = auth.currentUser;

  // if (!user) {
  //   throw new Error("User not logged in");
  // }
  // const idToken = await user.getIdToken();
  // return idToken
}

export const BASE_URL =
  process.env.PROJECT_ENV === "production"
    ? "https://www.amuzn.app"
    : process.env.PROJECT_ENV === "development"
      ? "https://amuzn-webapp-dev.vercel.app"
      : "http://127.0.0.1:3000";

//export const BASE_URL ='https://www.amuzn.app/'; // PROD
export const GATE_URL = `/api/gate`;

export async function getLoggedInUserId() {
  const auth = getAuth();
  return new Promise((resolve, reject) => {
    onAuthStateChanged(auth, (user) => {
      if (user) {
        resolve(user.uid);
      } else {
        reject(new Error("User not logged in"));
      }
    });
  });
  //  const user = auth.currentUser;

  //  console.log({user , auth,check:auth?.currentUser,nnn:auth.name});

  // if (!user) {
  //   throw new Error("User not logged in");
  // }
  // return user?.uid
}

export const formatDate = (dateInput: any) => {
  let date: Date;
  if (
    dateInput &&
    typeof dateInput === "object" &&
    "_seconds" in dateInput &&
    "_nanoseconds" in dateInput
  ) {
    // Firestore Timestamp
    date = new Date(dateInput._seconds * 1000 + dateInput._nanoseconds / 1e6);
  } else if (
    dateInput &&
    typeof dateInput === "object" &&
    "seconds" in dateInput &&
    "nanoseconds" in dateInput
  ) {
    date = new Date(dateInput.seconds * 1000 + dateInput.nanoseconds / 1e6);
  } else {
    // ISO string or Date
    date = new Date(dateInput);
  }
  if (isNaN(date.getTime())) return "Invalid date";
  return format(date, "MMM d, yyyy h:mm a");
};

function expandList(base: string[], count: number) {
  const result = [];
  const repeats = Math.ceil(count / base.length);
  for (let i = 0; i < repeats; i++) {
    for (const word of base) {
      if (result.length >= count) break;
      result.push(`${word}${i}`);
    }
  }
  return result;
}

// Final lists
const adjectives = expandList(adjectivesBase, 1500);
const nouns = expandList(nounsBase, 1500);

export function generateUsername() {
  const adj = adjectives[Math.floor(Math.random() * adjectives.length)];
  const noun = nouns[Math.floor(Math.random() * nouns.length)];
  const number = Math.floor(Math.random() * 10000); // optional
  return `${adj}${noun}${number}`;
}

// export const _generateFileUrl = async (postFile: string | undefined): Promise<string | undefined> => {
//   if (!postFile) return undefined;

//   const { app } = await initFirebase();
//   const storage = getStorage(app);

//   // If postFile is a full URL, try to convert to SDK ref if possible
//   let filePath = postFile.startsWith("https://firebasestorage.googleapis.com/")
//     ? decodeURIComponent(postFile.split("/o/")[1].split("?")[0]) // extract path from URL
//     : postFile;

//   const fileRef = ref(storage, filePath);
//   const url = await getDownloadURL(fileRef);

//   return url;
// };

let fileUrlCache: Record<string, string> = {};


// old
export const generateFileUrl = (postFile: string | undefined): string | undefined => {


  if (!postFile) return undefined;

  // If already cached, return immediately
  if (fileUrlCache[postFile]) {
    return fileUrlCache[postFile];
  }

  // Return a placeholder first (so JSX doesn’t break)
  const placeholder = "";
  fileUrlCache[postFile] = placeholder;

  // Resolve async and cache the real URL
  (async () => {
    if (postFile.includes("https://ik.imagekit.io")) {
      fileUrlCache[postFile] = postFile;
    } else {
      const { app } = await initFirebase();
      const storage = getStorage(app);

      let filePath = postFile.startsWith("https://firebasestorage.googleapis.com/")
        ? decodeURIComponent(postFile.split("/o/")[1].split("?")[0])
        : postFile;

      const fileRef = ref(storage, filePath);
      console.log({ fileRef });

      const url = await getDownloadURL(fileRef);

      fileUrlCache[postFile] = url;
    }

    // Force React re-render (optional, if you use a global store / context / state setter here)
  })();

  return placeholder;
};


// idle funciton
export const _generateFileUrl = async (postFile: string | undefined): Promise<string | undefined> => {
    if(!postFile) { 
      return undefined;
    }

    let url;
    if (postFile.includes("https://ik.imagekit.io")) {
      return postFile
    } else {
      const { app } = await initFirebase();
      const storage = getStorage(app);

      let filePath = postFile.startsWith("https://firebasestorage.googleapis.com/")
        ? decodeURIComponent(postFile.split("/o/")[1].split("?")[0])
        : postFile;

      const fileRef = ref(storage, filePath);

      url = await getDownloadURL(fileRef);
    }
    return url;
};
