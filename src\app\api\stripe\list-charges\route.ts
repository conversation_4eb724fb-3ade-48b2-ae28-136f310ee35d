import { NextRequest, NextResponse } from 'next/server';
import { Stripe } from 'stripe';
import { getStripeInstance } from '@/lib/stripe';

// stripeListCharges - Following Firebase Functions pattern
export async function POST(request: NextRequest) {
  try {
    const { paymentIntentId, isUS } = await request.json();

    if (!paymentIntentId) {
      return NextResponse.json({
        success: false,
        error: 'Payment Intent ID is required'
      }, { status: 400 });
    }

    console.log('🔍 ===== STRIPE LIST CHARGES REQUEST =====');
    console.log(`📋 Payment Intent ID: ${paymentIntentId}`);
    console.log(`🌍 Using US Stripe: ${isUS === 'true'}`);
    console.log('🕐 Timestamp:', new Date().toISOString());

    const isUSBool = typeof isUS === 'string' ? isUS === 'true' : Boolean(isUS);
    const stripeService = getStripeInstance(isUSBool);
    console.log('[stripe/list-charges] Routing', { region: isUSBool ? 'us' : 'row' });

    // List all charges for this payment intent
    const charges = await stripeService.charges.list({
      payment_intent: paymentIntentId,
      limit: 10,
      expand: ['data.payment_method_details', 'data.billing_details', 'data.outcome']
    });

    console.log('✅ STRIPE CHARGES RETRIEVED:');
    console.log(`📊 Total charges found: ${charges.data.length}`);

    // Log each charge details
    charges.data.forEach((charge, index) => {
      console.log(`🎯 CHARGE ${index + 1} DETAILS:`);
      console.log(`🆔 ID: ${charge.id}`);
      console.log(`📊 Status: ${charge.status}`);
      console.log(`💰 Amount: ${charge.amount}`);
      console.log(`💰 Amount Captured: ${charge.amount_captured}`);
      console.log(`💰 Amount Refunded: ${charge.amount_refunded}`);
      console.log(`💱 Currency: ${charge.currency}`);
      console.log(`✅ Paid: ${charge.paid}`);
      console.log(`📦 Captured: ${charge.captured}`);
      console.log(`↩️ Refunded: ${charge.refunded}`);
      console.log(`🕐 Created: ${new Date(charge.created * 1000).toISOString()}`);
      console.log(`📧 Receipt Email: ${charge.receipt_email}`);
      console.log(`🧾 Receipt URL: ${charge.receipt_url}`);
      console.log(`💳 Payment Method: ${charge.payment_method}`);
      console.log(`📝 Description: ${charge.description}`);

      if (charge.payment_method_details?.card) {
        console.log(`💳 CARD DETAILS for ${charge.id}:`);
        console.log(`🏷️ Brand: ${charge.payment_method_details.card.brand}`);
        console.log(`🔢 Last 4: ${charge.payment_method_details.card.last4}`);
        console.log(`📅 Exp: ${charge.payment_method_details.card.exp_month}/${charge.payment_method_details.card.exp_year}`);
        console.log(`🌍 Country: ${charge.payment_method_details.card.country}`);
        console.log(`💰 Funding: ${charge.payment_method_details.card.funding}`);
        console.log(`🔐 Network: ${charge.payment_method_details.card.network}`);
      }

      if (charge.billing_details) {
        console.log(`📍 BILLING DETAILS for ${charge.id}:`);
        console.log(`👤 Name: ${charge.billing_details.name}`);
        console.log(`📧 Email: ${charge.billing_details.email}`);
        console.log(`📞 Phone: ${charge.billing_details.phone}`);
        if (charge.billing_details.address) {
          console.log(`🏠 Address:`, charge.billing_details.address);
        }
      }

      if (charge.outcome) {
        console.log(`🔒 OUTCOME for ${charge.id}:`);
        console.log(`📊 Type: ${charge.outcome.type}`);
        console.log(`🌐 Network Status: ${charge.outcome.network_status}`);
        console.log(`⚠️ Risk Level: ${charge.outcome.risk_level}`);
        console.log(`🎯 Risk Score: ${charge.outcome.risk_score}`);
        console.log(`💬 Seller Message: ${charge.outcome.seller_message}`);
      }
    });

    // Following Firebase Functions response pattern
    const responseData = {
      success: true,
      isUSStripeUsed: Boolean(isUS === 'true'),
      charges: charges.data,
      latestChargeId: charges.data[0] ? charges.data[0].id : null,
      chargeCount: charges.data.length,
      timestamp: new Date().toISOString()
    };

    console.log('✅ ===== STRIPE LIST CHARGES RESPONSE =====');
    console.log(`🎯 Latest Charge ID: ${responseData.latestChargeId}`);
    console.log(`📊 Total charges: ${responseData.chargeCount}`);
    console.log(`🌍 US Stripe Used: ${responseData.isUSStripeUsed}`);
    console.log('🔚 ===== END STRIPE LIST CHARGES =====');

    return NextResponse.json(responseData);

  } catch (error) {
    console.error('❌ Error in stripe list charges:', error);
    
    if (error instanceof Stripe.errors.StripeError) {
      return NextResponse.json({
        success: false,
        error: 'Stripe API error',
        details: error.message,
        type: error.type
      }, { status: 400 });
    }

    return NextResponse.json({
      success: false,
      error: 'Failed to list charges',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

// GET method for convenience
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const paymentIntentId = searchParams.get('payment_intent_id');
    const isUS = searchParams.get('isUS') === 'true';

    if (!paymentIntentId) {
      return NextResponse.json({
        success: false,
        error: 'Payment Intent ID is required'
      }, { status: 400 });
    }

    // Convert to POST request format
    const postRequest = new NextRequest(request.url, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ paymentIntentId, isUS })
    });

    return POST(postRequest);

  } catch (error) {
    console.error('❌ Error in GET stripe list charges:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to list charges',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
