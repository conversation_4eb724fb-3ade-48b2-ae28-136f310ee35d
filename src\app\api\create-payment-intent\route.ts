import { NextRequest, NextResponse } from 'next/server';
import { Stripe } from 'stripe';
import { getOrderById } from '@/services/ordersServices';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY as string);

export async function POST(request: NextRequest) {
  try {
    const {
      amount,
      currency,
      productName,
      userId,
      sellerId,
      orderId,
      isEscrow = false,
      userEmail,
      userName,
      paymentMethodType = 'card'
    } = await request.json();

    if (!amount) {
      return NextResponse.json({ error: 'Amount is required' }, { status: 400 });
    }

    if (!currency || typeof currency !== 'string') {
      return NextResponse.json({ error: 'Currency is required' }, { status: 400 });
    }

    // Optionally include order uniqueId in metadata
    let orderUniqueId: string | undefined;
    if (orderId) {
      try {
        const orderRes = await getOrderById(orderId);
        if (orderRes.success && (orderRes as any).order?.uniqueId) {
          orderUniqueId = (orderRes as any).order.uniqueId as string;
        }
      } catch {}
    }

    const paymentIntent = await stripe.paymentIntents.create({
      amount: parseInt(amount.toString()),
      currency: String(currency).toLowerCase(),
      automatic_payment_methods: {
        enabled: true,
        // For redirect-based methods like Stripe Crypto, allow redirects when not using escrow
        allow_redirects: isEscrow ? 'never' : 'always',
      },
      capture_method: isEscrow ? 'manual' : 'automatic',
      metadata: {
        ...(userId && { userId }),
        ...(sellerId && { sellerId }),
        ...(orderId && { orderId }),
        ...(orderUniqueId ? { orderUniqueId } : {}),
        ...(productName && { productName }),
        ...(userEmail && { userEmail }),
        ...(userName && { userName }),
        isEscrow: isEscrow.toString(),
        paymentMethodType: paymentMethodType || 'card',
      },
      description: `${isEscrow ? 'Escrow payment' : 'Payment'} for ${productName || 'product'}${(orderUniqueId || orderId) ? ` - Order ${orderUniqueId || orderId}` : ''}`,
      receipt_email: userEmail || undefined,
    });

    return NextResponse.json({
      success: true,
      clientSecret: paymentIntent.client_secret,
      paymentIntentId: paymentIntent.id,
    });
  } catch (error) {
    console.error('Error creating payment intent:', error);
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to create payment intent'
      },
      { status: 500 }
    );
  }
}