@tailwind base;
@tailwind components;
@tailwind utilities;

/* Skeleton loading animation */
@keyframes skeletonAnimation {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

.skeleton-animation {
  animation: skeletonAnimation 1.5s infinite;
}

/* Fade in up animation for loader */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in-up {
  animation: fadeInUp 0.5s ease-out forwards;
}

/* Mobile location dropdown styles */
@media (max-width: 768px) {
  .mobile-dropdown {
    box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.15) !important;
    border-radius: 12px !important;
    border-width: 2px !important;
  }

  .mobile-dropdown .sticky {
    padding: 10px !important;
    font-size: 12px !important;
    font-weight: 600 !important;
  }
}

/* #### SF UI Text Font Faces #### */
@font-face {
  font-family: "SF UI Text";
  font-style: normal;
  font-weight: 400;
  src: url("/assets/font/SFUIText-Regular.woff") format("woff");
}

@font-face {
  font-family: "SF UI Text";
  font-style: italic;
  font-weight: 400;
  src: url("/assets/font/SFUIText-RegularItalic.woff") format("woff");
}

@font-face {
  font-family: "SF UI Text";
  font-style: normal;
  font-weight: 500;
  src: url("/assets/font/SFUIText-Medium.woff") format("woff");
}

@font-face {
  font-family: "SF UI Text";
  font-style: italic;
  font-weight: 500;
  src: url("/assets/font/SFUIText-MediumItalic.woff") format("woff");
}

@font-face {
  font-family: "SF UI Text";
  font-style: normal;
  font-weight: 600;
  src: url("/assets/font/SFUIText-Semibold.woff") format("woff");
}

@font-face {
  font-family: "SF UI Text";
  font-style: italic;
  font-weight: 600;
  src: url("/assets/font/SFUIText-SemiboldItalic.woff") format("woff");
}

@font-face {
  font-family: "SF UI Text";
  font-style: normal;
  font-weight: 700;
  src: url("/assets/font/SFUIText-Bold.woff") format("woff");
}

@font-face {
  font-family: "SF UI Text";
  font-style: italic;
  font-weight: 700;
  src: url("/assets/font/SFUIText-BoldItalic.woff") format("woff");
}

@font-face {
  font-family: "SF UI Text";
  font-style: normal;
  font-weight: 900;
  src: url("/assets/font/SFUIText-Heavy.woff") format("woff");
}

@font-face {
  font-family: "SF UI Text";
  font-style: italic;
  font-weight: 900;
  src: url("/assets/font/SFUIText-HeavyItalic.woff") format("woff");
}

/* Apply the font globally */
body {
  font-family: "SF UI Text", sans-serif;
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 240 10% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;
    --primary: 240 5.9% 10%;
    --primary-foreground: 0 0% 98%;
    --secondary: 240 4.8% 95.9%;
    --secondary-foreground: 240 5.9% 10%;
    --muted: 240 4.8% 95.9%;
    --muted-foreground: 240 3.8% 46.1%;
    --accent: 240 4.8% 95.9%;
    --accent-foreground: 240 5.9% 10%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 5.9% 90%;
    --input: 240 5.9% 90%;
    --ring: 240 10% 3.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
  }
  .dark {
    --background: 240 10% 3.9%;
    --foreground: 0 0% 98%;
    --card: 240 10% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 240 10% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 240 5.9% 10%;
    --secondary: 240 3.7% 15.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 240 3.7% 15.9%;
    --muted-foreground: 240 5% 64.9%;
    --accent: 240 3.7% 15.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 3.7% 15.9%;
    --input: 240 3.7% 15.9%;
    --ring: 240 4.9% 83.9%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

.hide-scroll {
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* Internet Explorer 10+ */
}

.hide-scroll::-webkit-scrollbar {
  /* Webkit browsers */
  display: none;
}

.hide-scroll-custom::-webkit-scrollbar-thumb {
  background-color: #888; /* Color of the scrollbar thumb */
  border-radius: 12px; /* Fully rounded scrollbar thumb */
  border: 3px solid #f1f1f1; /* Padding around the thumb for spacing */
}
.hide-scroll-custom::-webkit-scrollbar-button {
  display: none; /* Hides the up and down arrows */
}
.hide-scroll-custom {
  scrollbar-width: thin;
  scrollbar-color: #e3e3e3 #f1f1f1;
}

.hide-scroll-custom::-webkit-scrollbar {
  scrollbar-width: 4px;
  border-radius: 10px;
}

.chat-scroll-custom::-webkit-scrollbar {
  width: 5px; /* Thinnest scrollbar */
  min-height: 20px; /* Minimum scrollbar height */
  max-height: 50px; /* Maximum scrollbar height */
}

.chat-scroll-custom::-webkit-scrollbar-thumb {
  background: #454545; /* Gray scrollbar */
  border-radius: 10px;
}

.chat-scroll-custom::-webkit-scrollbar-track {
  background: lightgray; /* Light gray track */
  border-radius: 0 0 10px 10px;
}

.chat-scroll-custom::-webkit-scrollbar-button {
  display: none; /* Removes up/down buttons */
}

p {
  @apply text-black text-base;
}

/* iPhone/iOS Video Fixes */
video {
  -webkit-playsinline: true;
  -webkit-appearance: none;
  background-color: transparent;
}

/* iOS Textarea and Input Fixes */
.ios-textarea-fix {
  -webkit-appearance: none;
  -webkit-border-radius: 8px;
  border-radius: 8px;
  -webkit-transform: translateZ(0);
  transform: translateZ(0);
}

/* Prevent iOS zoom on input focus */
@supports (-webkit-touch-callout: none) {
  input[type="text"],
  input[type="email"],
  input[type="password"],
  input[type="number"],
  input[type="tel"],
  input[type="url"],
  input[type="search"],
  textarea,
  select {
    font-size: 16px !important;
    -webkit-appearance: none;
    -webkit-border-radius: 0;
    border-radius: 0;
  }

  .ios-textarea-fix {
    font-size: 16px !important;
    -webkit-appearance: none;
    -webkit-border-radius: 8px !important;
    border-radius: 8px !important;
  }
}

/* iOS Safari specific fixes */
@media screen and (-webkit-min-device-pixel-ratio: 0) {
  .ios-textarea-fix:focus {
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
    -webkit-perspective: 1000;
    perspective: 1000;
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
  }
}

/* Drawer header iOS fixes - Conservative approach */
.drawer-header.ios-fixed {
  position: sticky !important;
  top: 0 !important;
  z-index: 1000 !important;
  background-color: white !important;
  -webkit-transform: translateZ(0);
  transform: translateZ(0);
}

/* Additional iOS fixes for body when textarea is focused */
body.ios-textarea-focused {
  position: fixed !important;
  width: 100% !important;
  overflow: hidden !important;
  transition:
    position 0.3s ease-out,
    overflow 0.3s ease-out;
}

/* Prevent layout shifts during iOS transitions - Removed transitions to avoid jumps */

/* iOS scrollable drawer body - No transitions to prevent layout jumps */
.drawer-body.ios-scrollable {
  overflow-y: auto !important;
  -webkit-overflow-scrolling: touch !important;
  max-height: calc(100vh - 120px) !important;
  padding-bottom: 20px !important;
}

/* Smooth scrolling for iOS */
.ios-scrollable {
  -webkit-overflow-scrolling: touch;
  overflow-scrolling: touch;
  scroll-behavior: smooth;
}

/* Drawer body - No transitions to prevent layout jumps during iOS blur */

/* Ensure drawer content is accessible on iOS */
@media screen and (max-device-width: 768px) {
  .drawer-body.ios-scrollable {
    /* Adjust for virtual keyboard */
    max-height: calc(100vh - 140px) !important;
  }

  /* Make sure the footer/confirm button is always accessible */
  .drawer-body.ios-scrollable .p-4.border-t {
    position: sticky;
    bottom: 0;
    background-color: white;
    z-index: 100;
    margin-top: auto;
  }
}

/* iOS keyboard appearance fixes */
@media screen and (max-device-width: 768px) {
  .ios-textarea-fix:focus {
    -webkit-user-select: text;
    user-select: text;
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
  }

  /* Ensure drawer maintains proper height when keyboard appears */
  .drawer-content {
    height: 100vh !important;
    display: flex !important;
    flex-direction: column !important;
  }

  /* Make drawer body flexible and scrollable */
  .drawer-body {
    flex: 1 !important;
    overflow-y: auto !important;
    -webkit-overflow-scrolling: touch !important;
  }

  /* Ensure footer stays at bottom */
  .drawer-body > div {
    min-height: 100% !important;
    display: flex !important;
    flex-direction: column !important;
  }

  /* Sticky footer behavior */
  .sticky.bottom-0 {
    position: sticky !important;
    bottom: 0 !important;
    margin-top: auto !important;
  }

  /* Removed transitioning classes to prevent layout jumps */
}

/* Ensure video poster images display properly on iPhone */
video::-webkit-media-controls-start-playback-button {
  display: none !important;
  -webkit-appearance: none;
}

/* Fix video aspect ratio on iPhone */
video[poster] {
  object-fit: cover;
  background-size: cover;
  background-position: center;
}

/* Lightbox video fixes for iPhone */
.yarl__video_container video {
  -webkit-playsinline: true !important;
  playsinline: true !important;
}

.custom-overlay-chat {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  /* left: 22rem; Match the sheet's left margin */
  background-color: white;
  z-index: 50;
  pointer-events: auto;
  transition: opacity 0.3s ease-in-out;
}

.filter-list {
  @apply flex flex-row items-center justify-between border-b-[1px] border-borderColor  w-full px-4 pb-4 cursor-pointer;
}

.row {
  @apply flex flex-row items-center;
}

/* Custom class for clipping the 5th image */
.overflow-x-clip {
  overflow-x: hidden;
  /* mask-image: linear-gradient(
    to right,
    rgba(0, 0, 0, 1) 0%,
    rgba(0, 0, 0, 1) 95%,
    rgba(0, 0, 0, 0) 100%
  );
  -webkit-mask-image: linear-gradient(
    to right,
    rgba(0, 0, 0, 1) 0%,
    rgba(0, 0, 0, 1) 95%,
    rgba(0, 0, 0, 0) 100%
  ); */
}

.primary-tabs[data-state="active"] {
  background-color: #333333 !important;
  border-radius: 100px;
  color: white !important;
  @apply text-red-700;
}

.primary-tabs [data-state="inactive"] {
  background-color: white !important;
  border-radius: 100px;
  color: white !important;
  @apply text-red-700;
}

.selected-tabs[data-state="active"] {
  /* background-color: aqua !important; */

  color: var(--violet-11);
  box-shadow:
    inset 0 -1px 0 0 red,
    0 1px 0 0 green;
}

.h-9 {
  height: 33px;
}

.btn-sm {
  font-size: 0.875rem;
  @apply py-[0.25rem] px-[0.5rem] text-center;
}

.btn:hover {
  background-color: #333333 !important;
  color: white !important;
}

.btn-xs {
  @apply rounded-full h-[25px]  cursor-pointer text-[14px] font-[400] px-6 row justify-center border-primary;
}

.text-iconColor {
  @apply stroke-[1.7px];
}

/* for tabs customization */
.TabsList {
  flex-shrink: 0;
  display: flex;
  border-bottom: 1px solid var(--mauve-6);
}

.TabsTrigger {
  background-color: #fafafa;
  padding: 0 20px;
  height: 45px;
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 15px;
  line-height: 1;
  border-bottom: 2px solid white;
  color: #a29e9e;
}

.small-subheading {
  @apply text-subtitle1 max-md:text-sm -mt-1;
}
.small-heading {
  @apply font-bold text-lg max-md:text-sm;
}
.TabsTrigger[data-state="active"] {
  border-bottom: 2px solid var(--active-border-color, #333333); /* Use CSS variable */
  outline: none;
  color: var(--active-border-color, #333333);
}

.TabsTrigger[data-state="active"] div p {
  color: var(--active-border-color, #333333); /* Use the same color for text */
}

.TabsTrigger:focus {
  position: relative;
  /* box-shadow: 0 0 0 2px black; */
}

/* tabsListBg */
.TabsListBg {
  flex-shrink: 0;
  display: flex;
  /* border-bottom: 1px solid var(--mauve-6); */
  border: 1px solid #e3e3e3;
  border-radius: 100px;
  padding: 2px;
}

.TabsTriggerBg {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  padding: 3px 0;
  color: #454545;
}

.TabsTriggerBg[data-state="active"] {
  color: white;
  outline: none;
  background-color: var(--active-bg-color, #333333);
  border-radius: 100px;
}
.TabsTriggerBg:focus {
  position: relative;
  /* box-shadow: 0 0 0 2px black; */
}

/* tabs with no background */
.TabsTriggernoBg {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  padding: 3px 0;
  color: #454545;
}

.TabsTriggernoBg[data-state="active"] {
  color: #333333;
  outline: none;
  background-color: #ad2525;
  border-radius: 100px;
}
.TabsTriggernoBg:focus {
  position: relative;
  /* box-shadow: 0 0 0 2px black; */
}

/* Important class */
.dynamic-dialog {
  @apply min-w-96 max-w-96 min-h-60 p-6 rounded-[4rem] max-md:w-full max-md:h-[85%] max-md:max-h-[85%] overflow-auto max-md:absolute max-md:bottom-12 max-md:rounded-[0px];
}

.custom-overlay {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 22rem; /* Match the sheet's left margin */
  background-color: rgba(0, 0, 0, 0.8);
  z-index: 40;
  pointer-events: auto;
  transition: opacity 0.3s ease;
}

.hover\:bg-accent:hover {
  background-color: transparent !important;
}

.prgress .bg-primary {
  --tw-bg-opacity: 1;
  background-color: var(--active-bg-progress, #333333);
}

.css-1ocvkcr {
  background: transparent !important;
}
.text-shadow {
  text-shadow: 0px 1px 0px rgba(0, 0, 0, 0.25);
}

/* modal style */

.modal-content-large {
  @apply min-w-96  min-h-60 p-0 py-4  max-md:min-w-full rounded-3xl max-md:rounded-bl-none max-md:rounded-br-none max-md:m-0;
}
.modal-content {
  @apply max-md:w-full px-0 py-5 rounded-3xl max-md:rounded-bl-none max-md:rounded-br-none max-md:m-0;
}

.tap-highlight-transparent {
  @apply text-2xl max-md:text-xl;

  border: 1px solid #333333;
  background-color: white;
  /* font-size: 30px; */
  z-index: 999999;
}
/* react calendor customization */

.react-calendar {
  height: 100%;
  min-height: 100%;
  background: white;
  border: none;
  font-family: "Arial", "Helvetica", sans-serif;
  line-height: 1.125em;
  background-color: var(--active-bg-color-calender, #333333);
  @apply w-[100%] min-w-[100%] max-w-[100%] max-md:w-[100%] max-md:min-w-[100%] max-md:max-w-[100%];
}

/* Default active date background */
.react-calendar__tile--active {
  background-color: var(--active-bg-color-calender, #333333) !important;
  color: white !important;
  border-radius: 8px; /* Optional: Makes it rounded */
}

/* Change background on hover/focus when active */
.react-calendar__tile--active:enabled:hover,
.react-calendar__tile--active:enabled:focus {
  background-color: var(--active-bg-color-calender, #333333) !important; /* Slightly darker pink */
  color: white !important;
}

.react-calendar button {
  flex: 0 0 0%;
  /* margin: 0; */
  border: 0;
  outline: none;
  aspect-ratio: 1/1;
  border-radius: 100%;
  background-color: none;

  overflow: auto;
  @apply text-2xl font-medium max-md:text-xl max-md:font-normal max-md:text-center;
}

@media (max-width: 960px) {
  .react-calendar button {
    width: 40px !important;
    height: 40px !important;
    font-size: 1.25rem; /* text-xl */
    font-weight: 400; /* font-normal */
    text-align: center;
  }
}
/* .react-calendar__month-view__days button {
  flex: 0 0 10%;
} */

.react-calendar button abbr {
  text-align: center;
  aspect-ratio: 1/1 !important;
  width: 40px !important;
  height: 40px !important;
  min-width: 40px !important;
  min-height: 40px !important;
  /* background-color: green; */
}

abbr {
  text-decoration: none;
  /* background-color: red;
  width: 40px;
  height: 40px;
  min-width: 40px;
  min-height: 40px;
  padding: 10px;
  aspect-ratio: 1/1;
  border-radius: 100%; */
}

.highlight {
  background-color: var(--active-bg-color-calender, green) !important;
  color: white !important;
  border-radius: 50%;
}

/* test hide the calender view */
.custom-calendar .react-calendar {
  @apply w-[100%] min-w-[100%] max-w-[100%] max-md:w-[100%] max-md:min-w-[100%] max-md:max-w-[100%] border-none;
}
.hidden-calendar .react-calendar__month-view {
  display: none;
}

.hidden-calendar .react-calendar__year-view {
  display: none;
}

.hidden-calendar .react-calendar__decade-view {
  display: none;
}

.hidden-calendar .react-calendar__century-view {
  display: none;
}

.month-text {
  font-size: 1.5rem;
  font-weight: bold;
  text-align: center;
  margin-top: 10px;
}

.TabsTriggerBg {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  /* font-size: 20px; */
  padding: 3px 3px;
  color: #454545;
  @apply text-[20px] max-md:text-[14px];
}

/* for */
.react-calendar__month-view__days__day--weekend {
  color: #333 !important;
}

.react-calendar__tile--now {
  background: #4f4f4f !important;
  color: white !important;
  border: 2px solid rgba(66, 73, 66, 1) !important;
}

/* .react-calendar__month-view__days__day--neighboringMonth,
.react-calendar__decade-view__years__year--neighboringDecade,
.react-calendar__century-view__decades__decade--neighboringCentury {
  color: white !important;
} */

/* .react-calendar__tile:enabled:hover,
.react-calendar__tile:enabled:focus {
  background-color: none !important;
} */

.react-calendar__navigation button:enabled:hover,
.react-calendar__navigation button:enabled:focus {
  background-color: white !important;
}

.indicatorColor {
  background-color: royalblue;
}

.bg-default-400 {
  --tw-bg-opacity: 1;
  background-color: var(--active-bg-color-indicator, #333333);
}

.bg-color-indicator-plus {
  --tw-bg-opacity: 1;
  background-color: var(--active-bg-color-indicator-plus, #333333);
}

/* Custom styles for drawer close button */
.drawer-close-button {
  background-color: transparent !important;
  transition: background-color 0.2s ease;
  border: none !important;
}

.drawer-close-button:hover {
  background-color: rgba(0, 0, 0, 0.05) !important;
}

/* Adjacent drawer styles */
.adjacent-drawer {
  position: fixed !important;
  left: 22rem !important; /* Position it right next to the sidebar */
  z-index: 50 !important;
  width: 26.25rem !important;
  max-width: calc(100% - 22rem) !important;
  border-left: 1px solid #e5e7eb !important;
  box-shadow: -2px 0 10px rgba(0, 0, 0, 0.05) !important;
}

/* Nested drawer styles */
.nested-drawer {
  position: fixed !important;
  left: 48.25rem !important; /* Position it right next to the adjacent drawer */
  z-index: 51 !important;
  width: 26.25rem !important;
  max-width: calc(100% - 48.25rem) !important;
  border-left: 1px solid #e5e7eb !important;
  box-shadow: -2px 0 10px rgba(0, 0, 0, 0.05) !important;
}

/* Nested drawer styles */
.nested-drawer-trion {
  position: fixed !important;
  left: 74.5rem !important; /* Position it right next to the adjacent drawer */
  z-index: 52 !important;
  width: 26.25rem !important;
  max-width: calc(100% - 74.5rem) !important;
  border-left: 1px solid #e5e7eb !important;
  box-shadow: -2px 0 10px rgba(0, 0, 0, 0.05) !important;
}

/* Custom styling for the drawer content */
.adjacent-drawer .drawer-content,
.nested-drawer .drawer-content {
  background-color: white !important;
  border-radius: 0 !important;
}

/* Remove rounded corners from drawers */
.adjacent-drawer [data-drawer-content],
.nested-drawer [data-drawer-content] {
  border-radius: 0 !important;
}

/* Remove rounded corners from drawer overlay */
.adjacent-drawer [data-drawer-overlay],
.nested-drawer [data-drawer-overlay] {
  border-radius: 0 !important;
}

/* Remove rounded corners from drawer container */
.adjacent-drawer [data-drawer-container],
.nested-drawer [data-drawer-container] {
  border-radius: 0 !important;
}

/* Global override for all drawer elements */
[data-drawer-content],
[data-drawer-overlay],
[data-drawer-container],
.drawer-content,
.drawer-body,
.drawer-header {
  border-radius: 0 !important;
}

/* On mobile, position it at the left edge */
@media (max-width: 1024px) {
  .adjacent-drawer {
    left: 0 !important;
    max-width: 100% !important;
    border-left: none !important;
    box-shadow: none !important;
  }

  .nested-drawer {
    left: 0 !important;
    max-width: 100% !important;
    border-left: none !important;
    box-shadow: none !important;
  }
}
