import { useState, useEffect } from "react";
import { initFirebase } from "../../firebaseConfig";
import { getStorage, ref, getDownloadURL } from "firebase/storage";

// Cache to store resolved URLs globally
let urlCache: Record<string, string> = {};

const useGenerateUrl = (postFile: string | undefined) => {
  const [url, setUrl] = useState<string | undefined>(undefined);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (!postFile) {
      setUrl(undefined);
      return;
    }

    // If already cached, return immediately
    if (urlCache[postFile]) {
      setUrl(urlCache[postFile]);
      return;
    }

    // Handle ImageKit URLs directly
    if (postFile.includes("https://ik.imagekit.io")) {
      urlCache[postFile] = postFile;
      setUrl(postFile);
      return;
    }

    // Handle full Firebase Storage URLs directly
    if (postFile.startsWith("https://firebasestorage.googleapis.com/")) {
      urlCache[postFile] = postFile;
      setUrl(postFile);
      return;
    }

    // Try to construct URL using BASE_STORAGE_URL for simple file paths
    const baseUrl = process.env.BASE_STORAGE_URL;
    if (baseUrl && !postFile.includes("/") && !postFile.includes("?")) {
      const constructedUrl = `${baseUrl}${encodeURIComponent(postFile)}?alt=media`;
      urlCache[postFile] = constructedUrl;
      setUrl(constructedUrl);
      return;
    }

    // For complex Firebase paths, resolve using Firebase SDK
    setIsLoading(true);
    const resolveFirebaseUrl = async () => {
      try {
        const { app } = await initFirebase();
        const storage = getStorage(app);

        let filePath = postFile.startsWith("https://firebasestorage.googleapis.com/")
          ? decodeURIComponent(postFile.split("/o/")[1].split("?")[0])
          : postFile;

        const fileRef = ref(storage, filePath);
        const resolvedUrl = await getDownloadURL(fileRef);

        urlCache[postFile] = resolvedUrl;
        setUrl(resolvedUrl);
      } catch (error) {
        console.error("Error resolving Firebase URL:", error);
        // Set empty string to prevent repeated attempts
        urlCache[postFile] = "";
        setUrl(undefined);
      } finally {
        setIsLoading(false);
      }
    };

    resolveFirebaseUrl();
  }, [postFile]);

  return { url, isLoading };
};

export default useGenerateUrl;
