# iOS Mobile Browser Fixes - Enhanced with Scrolling Support

This document outlines the iOS-specific fixes implemented to resolve mobile browser issues with textarea elements, including proper scrolling behavior to maintain content accessibility.

## Issues Addressed

### 1. Automatic Zoom on Textarea Focus

**Problem**: iOS Safari automatically zooms in when a textarea with font-size less than 16px is focused.
**Solution**:

- Set `font-size: 16px` for all textarea elements on iOS devices
- Use CSS `@supports (-webkit-touch-callout: none)` to target iOS specifically
- Apply `-webkit-appearance: none` to prevent default iOS styling

### 2. Header Disappearing on Keyboard Appearance

**Problem**: When the virtual keyboard appears, the page header disappears due to viewport changes.
**Solution**:

- Fix the body position when textarea is focused
- Store the original scroll position
- Apply `position: sticky` to the drawer header
- Use `translateZ(0)` for hardware acceleration

### 3. Header Not Reappearing After Keyboard Dismissal

**Problem**: After the keyboard is dismissed, the header doesn't reappear properly.
**Solution**:

- Restore original viewport meta tag
- Remove fixed positioning from body
- Restore original scroll position
- Reset drawer header styles

### 4. Content Below Textarea Becoming Inaccessible ⭐ NEW

**Problem**: When the virtual keyboard appears, content below the textarea (like the Confirm button) becomes hidden and inaccessible.
**Solution**:

- Enable scrolling within the drawer body while preventing body scroll
- Make the footer sticky to ensure it's always accessible
- Use `overflow-y: auto` and `-webkit-overflow-scrolling: touch` for smooth iOS scrolling
- Automatically scroll textarea into view when focused

### 5. Layout Jumps and Visual Shifts on Blur ⭐ ULTRA-CONSERVATIVE FIX

**Problem**: When the textarea loses focus, the layout shifts unexpectedly with visual jumps and positioning changes.
**Solution**:

- Store comprehensive original state (body styles, scroll positions, drawer state)
- Restore elements to their exact original state without intermediate changes
- Remove all CSS transitions that could cause layout shifts
- Use instant restoration instead of animated transitions
- Maintain drawer scroll position to preserve user context

## Implementation

### Key Features

1. **Drawer Body Scrolling**: The drawer body becomes scrollable when textarea is focused on iOS
2. **Sticky Footer**: The Confirm button footer stays accessible at the bottom
3. **Smooth Scrolling**: Native iOS momentum scrolling for better UX
4. **Auto-scroll**: Textarea automatically scrolls into view when focused
5. **Layout Preservation**: No visual jumps or layout shifts when keyboard appears/disappears
6. **Zero Layout Shifts**: Ultra-conservative restoration that maintains exact original state ⭐ ENHANCED
7. **Comprehensive State Storage**: Stores all original styles and positions for perfect restoration ⭐ NEW
8. **Instant Restoration**: No animations or transitions that could cause visual jumps ⭐ NEW

### Files Modified

1. **`src/components/orders/ChangeStatusDrawer.tsx`**

   - Added `overflow-y-auto` to content area
   - Made footer sticky with `sticky bottom-0 z-10`
   - Integrated enhanced iOS textarea handling

2. **`src/utils/iosUtils.ts`** (Enhanced)

   - Added drawer body scroll management
   - Implemented auto-scroll functionality
   - Enhanced cleanup for drawer-specific elements

3. **`src/app/globals.css`** (Enhanced)

   - Added `.ios-scrollable` class for drawer body
   - Implemented responsive drawer height calculations
   - Added sticky footer behavior for iOS

4. **`src/components/ui/nested-drawer.tsx`**
   - Added `drawer-body` class for targeting
   - Maintained existing scroll behavior

### Enhanced Functions

#### `handleIOSTextareaFocus()` - Enhanced

- Enables drawer body scrolling with `overflow-y: auto`
- Sets `max-height: calc(100vh - 120px)` for proper sizing
- Adds `ios-scrollable` class for CSS targeting
- Maintains header stickiness

#### `handleIOSTextareaBlur()` - Enhanced

- Resets drawer body scroll properties
- Removes `ios-scrollable` class
- Restores original layout without jumps

#### `useIOSTextareaFix()` - Enhanced

- Adds auto-scroll functionality with 300ms delay
- Uses `scrollIntoView()` to center textarea in viewport
- Maintains all existing iOS fixes

### CSS Classes - Enhanced

- `.ios-scrollable`: Applied to drawer body for smooth scrolling
- `.sticky.bottom-0.z-10`: Applied to footer for accessibility
- `.overflow-y-auto`: Applied to content area for scrolling
- `.drawer-body.ios-scrollable`: Specific iOS drawer scrolling styles

## Usage Example

```tsx
// The ChangeStatusDrawer now automatically handles scrolling
<ChangeStatusDrawer
  isOpen={isOpen}
  onOpenChange={setIsOpen}
  currentStatus="new"
  onStatusChange={handleStatusChange}
  orderType="received"
  // ... other props
/>
```

## Testing Scenarios

1. **Focus textarea on iOS**: Keyboard appears, header stays visible, content scrollable
2. **Scroll to Confirm button**: Button remains accessible via scrolling
3. **Blur textarea**: Keyboard dismisses, layout returns to normal, no jumps
4. **Multiple focus/blur cycles**: Consistent behavior without memory leaks

## Browser Support

- iOS Safari (all versions)
- iOS Chrome
- iOS Firefox
- iPad Safari (including iOS 13+ detection)

## Performance Optimizations

- Hardware acceleration with `translateZ(0)`
- Native momentum scrolling with `-webkit-overflow-scrolling: touch`
- Efficient DOM queries with specific selectors
- Minimal layout recalculations

## Troubleshooting

### Issue: Content still not scrollable

- Verify `.drawer-body` class is present
- Check if `ios-scrollable` class is applied during focus
- Ensure `overflow-y: auto` is not overridden

### Issue: Footer not sticky

- Verify footer has `sticky bottom-0 z-10` classes
- Check if parent container has proper flex layout
- Ensure z-index is not conflicting

### Issue: Textarea not auto-scrolling

- Verify 300ms timeout is sufficient for keyboard animation
- Check if `scrollIntoView` is supported
- Ensure textarea has `ios-textarea-fix` class

## Future Enhancements

1. Dynamic keyboard height detection
2. Configurable scroll behavior options
3. Support for multiple textareas in same drawer
4. Enhanced animation timing controls
