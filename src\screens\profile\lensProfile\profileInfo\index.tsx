"use client";
import {
  MoreH<PERSON>zontal,
  Edit2,
  X,
  Check,
  ChevronLeft,
  Triangle,
  Copy,
  Loader,
  Twitter,
  Facebook,
  Instagram,
  CheckCircle,
  Circle,
} from "react-feather";
import ProfileInfoHashtags from "./hashtags";
import ProfileInfoPersonalMotto from "./personalMotto";
import ProfileInfoSocialMedia from "./socialMedia";
import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogHeader,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Label } from "@radix-ui/react-label";
import { Textarea } from "@/components/ui/textarea";
import { useEffect, useRef, useState } from "react";
import { Input } from "@/components/ui/input";

import * as React from "react";

import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Badge } from "@/components/ui/badge";
import { themes } from "../../../../../theme";
import { Sheet } from "@/components/ui/sheet";
import { SheetContentNoClose } from "@/components/ui/sheet-no-close";
import { Progress } from "@/components/ui/progress";
import Link from "next/link";
import useProfile from "@/hook/profileData";
import useAuth from "@/hook";
import { updateUser } from "@/services/usersServices";
import { useUnFollow } from "@/lib/useUnfollow";
import { useFollow } from "@/lib/useFollow";
import { LENS_CONTRACT_ABI, LENS_CONTRACT_ADDRESS } from "@/const/contracts";
import { getId } from "@/services/authBridgeService";
import { initFirebase } from "../../../../../firebaseConfig";
import {
  PostReportReason,
  useFullAccountQuery,
  useReportPostMutation,
  useTransactionStatusQuery,
} from "@/graphql/test/generated";
import { getLocation } from "@/lib/utils";
import { Chip } from "@heroui/chip";
import AuthSignup from "@/screens/auth";
import { Modal, ModalBody, ModalContent } from "@heroui/react";
import SignInButton from "@/components/SignInButton";
import { useAccount } from "wagmi";
import { useUpdateFeed } from "@/lib/useUpdateFeed";
import { useSignInStore } from "@/components/GlobalSignInButton";
import { PublicationReportingReason } from "@/graphql/generated";

type Status = "follow" | "unfollow";
const Category = [
  {
    icon: "MoreHorizontal",
    title: "Music",
  },
  {
    icon: "MoreHorizontal",
    title: "Storytelling",
  },
  {
    icon: "MoreHorizontal",
    title: "Art",
  },
  {
    icon: "MoreHorizontal",
    title: "Theatre & Performance",
  },
  {
    icon: "MoreHorizontal",
    title: "Film & Photography",
  },
];

const ProfileInfo = (props: any) => {
  const [isOpen, setIsOpen] = useState(false);
  const [isOpenAbout, setIsOpenAbout] = useState(false);

  const [isOpenEditPhoto, setIsOpenEditPhoto] = useState(false);
  const [isOpenViewPhoto, setIsOpenViewPhoto] = useState(false);
  const [isOpenChangeCategories, setIsOpenChangeCategories] = useState(false);
  const [isOpenDelete, setIsOpenDelete] = useState(false);
  const [isOpenOtherDelete, setIsOpenOtherDelete] = useState(false);
  const [isOpenDeleteReason, setIsOpenDeleteReason] = useState(false);
  const [isSigninOpen, setIsSigninOpen] = useState(false);
  const [isSigninOpenweb2, setIsSigninOpenweb2] = useState(false);
  const [isOpenShare, setIsOpenShare] = useState(false);
  const [isCopied, setIsCopied] = useState(false);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);

  // States for category modal
  const [mainCat, setMainCat] = useState("");
  const [secondCat, setSecondCat] = useState("");
  const [isSavingCategories, setIsSavingCategories] = useState(false);
  const [categorySaveSuccess, setCategorySaveSuccess] = useState(false);

  const [isbothID, setIsbothID] = useState(false);

  const { address: isAuthW3 } = useAccount();
  const { address, isConnected } = useAccount();
  //   for Category
  const [SelectedCategory, setSelectedCategory] = useState(null);

  const handleItemClickCategory = (index: any) => {
    setSelectedCategory(index); // Set the clicked item as the selected one
    const themeEntries = Object.entries(themes);

    if (themeEntries[index]) {
      const selectedTheme = themeEntries[index][1]; // Extract the themeProperties
      setMainCat(selectedTheme.title);
    }
  };

  const handleItemClickCategorySecond = (index: any) => {
    const themeEntries = Object.entries(themes);

    if (themeEntries[index]) {
      const selectedTheme = themeEntries[index][1]; // Extract the themeProperties
      setSecondCat(selectedTheme.title);
    }
  };

  const handleSubmitChangeCategory = async () => {
    if (mainCat && secondCat) {
      try {
        setIsSavingCategories(true);
        setCategorySaveSuccess(false);

        // Create updated data object with categories
        const updatedData = {
          categories: [mainCat, secondCat],
        };

        // Call the update user API
        const response = await updateUser(auth?.userData?.uid, updatedData);

        if (response.success) {
          setCategorySaveSuccess(true);

          // Wait for success animation
          setTimeout(() => {
            // Refresh the data
            props.refetch();
            // Reset form data
            setMainCat("");
            setSecondCat("");
            // Close the modal
            setIsOpenChangeCategories(false);
            // Reset states
            setIsSavingCategories(false);
            setCategorySaveSuccess(false);
          }, 1500);
        } else {
          console.error("Error updating categories:", response.error);
          setIsSavingCategories(false);
        }
      } catch (error) {
        console.error("Error updating categories:", error);
        setIsSavingCategories(false);
      }
    }
  };

  let bgColor: any;

  {
    Object.entries(themes)
      .filter(([_, themeProperties]) => themeProperties.title === props.Category)
      .map(([themeName, themeProperties]) => (bgColor = themeProperties.backgroundColor));
  }

  const [isOpenSheet, setIsOpenSheet] = useState(false);

  // Function to toggle the sheet visibility
  const toggleSheet = () => {
    setIsOpenSheet(!isOpen);
  };

  const [isToggle, setIsToggle] = useState(false);

  const [progress, setProgress] = React.useState(13);

  React.useEffect(() => {
    const timer = setTimeout(() => setProgress(66), 500);
    return () => clearTimeout(timer);
  }, []);

  // fetch data from firebase
  const auth = useAuth();

  // for edit modal

  const [hashtags, setHashtags] = useState<string[]>([]);
  const [profileName, setProfileName]: any = useState("");

  // profile upload

  const [media, setMedia]: any = useState<File | null>(
    props.profileQueryData?.account?.metadata?.picture
  );
  const [isMedia, setIsMedia] = useState(false);

  const handleMediaUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files && event.target.files[0]) {
      setMedia(event.target.files[0]);
      setIsMedia(true);
      console.log({ feed, location, name, media });
    }
  };

  // handle count of follow and un-follow user

  //******************************  Lens Data fetch by id  ******************************

  // **************************** lens follow user *********************
  const { mutateAsync: followUser, isSuccess, data: followData } = useFollow();
  const [Txn_id, setTxn_id] = useState<string | null>(null);
  const [CurrentStatus, setCurrentStatus] = useState<Status>("follow");
  const [isLoading, setIsLoading] = useState(false);

  const unfollow = useUnFollow();
  const {
    data: profileData,
    isLoading: isLoadingProfile,
    error: profileQueryError,
    refetch,
  } = useFullAccountQuery(
    {
      accountRequest: {
        address: props.profileQueryData?.account?.address,
      },
      accountStatsRequest: {
        account: props.profileQueryData?.account?.address,
      },
    },
    {
      refetchOnWindowFocus: false,
    }
  );

  const [userId, setUserId] = useState("");
  const [lensId, setLensId] = useState("");

  const getLensUserId = async (otherUserID: any) => {
    console.log(props.isOtherProfile, otherUserID, auth?.lensUserId);

    try {
      const resp = await getId(
        { id: otherUserID }
        // { id: "0x08cfd6" }
      ); // send lens_code(web3) or user_id (web2)
      // console.log({ resp });
      if (resp) {
        // router.push(`profile/${resp.lens_code}`);
        setUserId(resp?.user_id);
        setLensId(resp?.lens_code);
      }

      if (resp?.user_id && resp.lens_id && resp.wallet_id) {
        // router.push(`profile/${resp.lens_code}`);
        setIsbothID(true);
      }
    } catch (error) {
      console.log({ error });
      setUserId("");
    }
  };

  useEffect(() => {
    // console.log(props?.otherUserID);

    getLensUserId(props?.otherUserID);
  }, [props, props?.otherUserID]);

  const profile = useProfile(userId);

  // share post
  function copyProfileLink(id: string) {
    const url = `https://www.amuzn.app/profile/lens/${id}`;
    navigator.clipboard
      .writeText(url)
      .then(() => {
        setIsCopied(true);
        setTimeout(() => {
          setIsCopied(false);
        }, 2000);
      })
      .catch((err) => {
        console.error("Failed to copy link: ", err);
      });
  }

  const [activeIcon, setActiveIcon] = useState("lens");
  const toggleIcon = () => {
    setActiveIcon(activeIcon === "amuzn" ? "lens" : "amuzn");
  };

  // handle update web-3 data
  const [name, setName] = useState(props.profileQueryData?.account?.metadata?.name);
  const [location, setLocation] = useState(
    getLocation(props.profileQueryData?.account?.metadata?.attributes)
  );
  const [feed, setFeed] = useState(props.profileQueryData?.account?.metadata?.bio);
  const [isLoaading, setIsLoaading] = useState(false);

  // Separate transaction IDs and success states for each modal
  const [aboutTxnId, setAboutTxnId] = useState<string | null>(null);
  const [aboutSuccess, setAboutSuccess] = useState(false);

  const [personalInfoTxnId, setPersonalInfoTxnId] = useState<string | null>(null);
  const [personalInfoSuccess, setPersonalInfoSuccess] = useState(false);

  const [photoTxnId, setPhotoTxnId] = useState<string | null>(null);
  const [photoSuccess, setPhotoSuccess] = useState(false);

  const feedResp = useUpdateFeed();

  const handleSubmitUpdateFeed = async (modalType: "about" | "personalInfo" | "photo") => {
    setIsLoaading(true);
    console.log({ feed, location, name, media });

    // Reset success states for all modals
    setAboutSuccess(false);
    setPersonalInfoSuccess(false);
    setPhotoSuccess(false);

    //  update web3 data
    const resp = await feedResp({
      feed,
      location,
      name,
      image_url: props.profileQueryData?.account?.metadata?.picture,
      image: media,
      upload_image: modalType === "photo",
    });

    if (resp.setAccountMetadata.__typename === "SetAccountMetadataResponse") {
      // Set the appropriate transaction ID based on modal type
      if (modalType === "about") {
        setAboutTxnId(resp.setAccountMetadata.hash);
        setPersonalInfoTxnId(null);
        setPhotoTxnId(null);
      } else if (modalType === "personalInfo") {
        setPersonalInfoTxnId(resp.setAccountMetadata.hash);
        setAboutTxnId(null);
        setPhotoTxnId(null);
      } else if (modalType === "photo") {
        setPhotoTxnId(resp.setAccountMetadata.hash);
        setAboutTxnId(null);
        setPersonalInfoTxnId(null);
      }

      // Also set the main transaction ID for backward compatibility
      setTxn_id(resp.setAccountMetadata.hash);
    }
  };

  const { data: transactionData, refetch: refetchTransactionStatus } = useTransactionStatusQuery(
    {
      request: {
        txHash: Txn_id,
      },
    },
    {
      enabled: !!Txn_id,
      refetchOnWindowFocus: false,
    }
  );

  useEffect(() => {
    // Check for About modal transaction
    if (aboutTxnId) {
      const POLL_INTERVAL = 1000;
      const MAX_CHECK_COUNT = 15;
      let checkCnt = 0;

      const intervalId = setInterval(async () => {
        if (checkCnt > MAX_CHECK_COUNT) {
          clearInterval(intervalId);
          setIsLoaading(false);
        }
        if (transactionData?.transactionStatus?.__typename === "FinishedTransactionStatus") {
          props.refetch();
          setAboutSuccess(true);
          setIsLoaading(false);

          // Auto-close after delay
          setTimeout(() => {
            setIsOpenAbout(false);
            setAboutSuccess(false);
            setAboutTxnId(null);
          }, 2000);

          clearInterval(intervalId);
        } else if (transactionData?.transactionStatus?.__typename === "FailedTransactionStatus") {
          setIsLoaading(false);
          setAboutTxnId(null);
          clearInterval(intervalId);
        } else {
          checkCnt++;
          await refetchTransactionStatus();
        }
      }, POLL_INTERVAL);

      return () => clearInterval(intervalId);
    }
  }, [aboutTxnId, transactionData, refetchTransactionStatus]);

  useEffect(() => {
    // Check for Personal Info modal transaction
    if (personalInfoTxnId) {
      const POLL_INTERVAL = 1000;
      const MAX_CHECK_COUNT = 15;
      let checkCnt = 0;

      const intervalId = setInterval(async () => {
        if (checkCnt > MAX_CHECK_COUNT) {
          clearInterval(intervalId);
          setIsLoaading(false);
        }
        if (transactionData?.transactionStatus?.__typename === "FinishedTransactionStatus") {
          props.refetch();
          setPersonalInfoSuccess(true);
          setIsLoaading(false);

          // Auto-close after delay
          setTimeout(() => {
            setIsOpen(false);
            setPersonalInfoSuccess(false);
            setPersonalInfoTxnId(null);
          }, 2000);

          clearInterval(intervalId);
        } else if (transactionData?.transactionStatus?.__typename === "FailedTransactionStatus") {
          setIsLoaading(false);
          setPersonalInfoTxnId(null);
          clearInterval(intervalId);
        } else {
          checkCnt++;
          await refetchTransactionStatus();
        }
      }, POLL_INTERVAL);

      return () => clearInterval(intervalId);
    }
  }, [personalInfoTxnId, transactionData, refetchTransactionStatus]);

  useEffect(() => {
    // Check for Photo modal transaction
    if (photoTxnId) {
      const POLL_INTERVAL = 1000;
      const MAX_CHECK_COUNT = 15;
      let checkCnt = 0;

      const intervalId = setInterval(async () => {
        if (checkCnt > MAX_CHECK_COUNT) {
          clearInterval(intervalId);
          setIsLoaading(false);
        }
        if (transactionData?.transactionStatus?.__typename === "FinishedTransactionStatus") {
          props.refetch();
          setPhotoSuccess(true);
          setIsLoaading(false);

          // Auto-close after delay
          setTimeout(() => {
            setIsOpenEditPhoto(false);
            setPhotoSuccess(false);
            setPhotoTxnId(null);
          }, 2000);

          clearInterval(intervalId);
        } else if (transactionData?.transactionStatus?.__typename === "FailedTransactionStatus") {
          setIsLoaading(false);
          setPhotoTxnId(null);
          clearInterval(intervalId);
        } else {
          checkCnt++;
          await refetchTransactionStatus();
        }
      }, POLL_INTERVAL);

      return () => clearInterval(intervalId);
    }
  }, [photoTxnId, transactionData, refetchTransactionStatus]);

  // In the parent component:
  const initialDataLoaded = useRef(false);

  useEffect(() => {
    if (!initialDataLoaded.current && props.profileQueryData?.account?.metadata) {
      // Only set these values on first load
      setName(props.profileQueryData.account.metadata.name || "");
      setLocation(getLocation(props.profileQueryData.account.metadata.attributes) || "");
      setFeed(props.profileQueryData.account.metadata.bio || "");

      // Mark that we've loaded initial data
      initialDataLoaded.current = true;
    }
  }, [props.profileQueryData]);

  useEffect(() => {
    const checkMobileView = () => {
      // Example: 768px is the breakpoint for mobile
      const isMobile = window.innerWidth <= 768;
      setIsToggle(isMobile);
    };

    checkMobileView(); // Initial check

    // Only update isToggle on resize if no input is focused
    // This prevents keyboard opening from triggering unwanted toggle
    const handleResize = () => {
      if (!document.activeElement?.tagName.match(/input|textarea/i)) {
        checkMobileView();
      }
    };

    window.addEventListener("resize", handleResize); // On resize

    // Clean up the event listener on unmount
    return () => {
      window.removeEventListener("resize", handleResize);
    };
  }, []);

  // Add this state to track local follow status
  const [localFollowStatus, setLocalFollowStatus] = useState<boolean | undefined>(undefined);

  // Initialize localFollowStatus from profileData when it loads
  useEffect(() => {
    if (profileData?.account?.operations) {
      setLocalFollowStatus(profileData.account.operations.isFollowedByMe);
    }
  }, [profileData]);

  // Add this useEffect to keep track of follow status changes
  useEffect(() => {
    // Update UI based on the latest data from the API
    if (profileData?.account?.operations) {
      setCurrentStatus(profileData.account.operations.isFollowedByMe ? "unfollow" : "follow");
    }
  }, [profileData]);

  // Handle Report
  const resonData = ["FRAUD", "ILLEGAL", "SENSITIVE", "SPAM"];
  function getReasonKey(reason: any) {
    switch (reason) {
      case PublicationReportingReason.Fraud:
        return "fraudReason";
      case PublicationReportingReason.Illegal:
        return "illegalReason";
      case PublicationReportingReason.Sensitive:
        return "sensitiveReason";
      case PublicationReportingReason.Spam:
        return "spamReason";
      default:
        return null;
    }
  }

  const { mutateAsync: reportPost } = useReportPostMutation();
  const [isResonPost, setIsResonPost] = useState(false);
  const [isCheck, setIsCheck] = useState(Array(0).fill(false));
  const [reportRespon, setReportReason] = useState("");
  const [reportComment, setReportComment] = useState("");

  const handleIsCheck = (id: number) => {
    setReportReason(resonData[id]);
    const newState = [...isCheck];
    newState[id] = !newState[id]; // Toggle the specific icon's state
    setIsCheck(newState);
  };

  return (
    <>
      <div
        className="text-primary px-3 shadow-2xl"
        style={{
          boxShadow: `var(--sds-size-depth-0) var(--sds-size-depth-100) var(--sds-size-depth-100) var(--sds-size-depth-negative-100) var(--sds-color-black-100)`,
        }}
      >
        <div className="row mb-3 gap-3">
          <div className="flex flex-row  gap-2">
            {props.isOtherProfile && (
              <div onClick={() => window.history.back()} className="">
                <ChevronLeft className="mt-2 cursor-pointer max-md:hidden" size={30} />
                <ChevronLeft className="mt-2 cursor-pointer md:hidden -mx-[6px]" size={24} />
              </div>
            )}

            <div
              onClick={() => props.isOtherProfile && setIsOpenViewPhoto(true)}
              className=" relative"
            >
              <img
                src={
                  props.profileQueryData?.account?.metadata?.picture ||
                  "https://static.hey.xyz/images/default.png"
                }
                alt=""
                className="w-[100px] h-[100px] min-w-[100px] min-h-[100px]  max-md:w-[74px] max-md:h-[74px] max-md:min-w-[74px] max-md:min-h-[74px]  rounded-full object-cover"
                style={{
                  border: "3px solid",
                  borderColor: bgColor,
                }}
              />
              {!props.isOtherProfile && (address || isConnected) && (
                <div className=" absolute bottom-0 right-1 w-7 h-7 row justify-center items-center rounded-full bg-white border-2 border-[#DEDEDE]">
                  <Edit2
                    color={bgColor}
                    className="cursor-pointer h-4 "
                    onClick={() => !props.isOtherProfile && setIsOpenEditPhoto(true)}
                    strokeWidth="3px"
                  />
                </div>
              )}
            </div>
          </div>
          <div className="w-full">
            <div className=" row justify-between">
              <div>
                <div>
                  <p className="font-bold text-xl max-md:text-base">
                    {props.profileQueryData?.account?.metadata?.name || "Profile Name"}{" "}
                  </p>
                  <p className="text-[#616770] -mt-[4px] max-md:-mt-[6px] max-md:text-[12px]">
                    {getLocation(props.profileQueryData?.account?.metadata?.attributes) ||
                      props.profileQueryData?.account?.username?.localName ||
                      "location*"}
                  </p>
                </div>
              </div>
              <div className="flex flex-col justify-between items-end ">
                <div className="pr-0  row gap-3">
                  <MoreHorizontal className=" cursor-pointer md:hidden" onClick={toggleSheet} />
                  <DropdownMenu open={isDropdownOpen} onOpenChange={setIsDropdownOpen}>
                    <DropdownMenuTrigger asChild>
                      <MoreHorizontal className=" cursor-pointer max-md:hidden" />
                    </DropdownMenuTrigger>
                    <DropdownMenuContent className="w-60 rounded-3xl">
                      <DropdownMenuLabel
                        className="text-center font-normal text-base border-b-2 py-3 cursor-pointer"
                        onClick={() => {
                          setIsOpenShare(true);
                          setIsDropdownOpen(false); // Close dropdown when opening share modal
                        }}
                      >
                        Share Profile
                      </DropdownMenuLabel>

                      {/* {!props.isOtherProfile && ( */}
                      <DropdownMenuLabel
                        className="text-center font-normal text-base cursor-pointer"
                        // onClick={() => setIsOpenDelete(true)}
                        onClick={() => {
                          address ? setIsResonPost(true) : setIsDropdownOpen(false),
                            useSignInStore.getState().setIsOpen(true);
                        }}
                      >
                        Report Profile
                      </DropdownMenuLabel>
                      {/* )} */}

                      <DropdownMenuSeparator />
                    </DropdownMenuContent>
                  </DropdownMenu>

                  {!props.isOtherProfile && (address || isConnected) && (
                    <Edit2
                      color={bgColor}
                      className="cursor-pointer"
                      onClick={() => setIsOpen(true)}
                    />
                  )}
                </div>
              </div>
            </div>

            <div className=" row justify-between max-md:-mt-1">
              <div className="row gap-3">
                <div
                  className=" cursor-pointer"
                  onClick={() => {
                    props.setSelectedTabs("Profiles"),
                      props.setSelectedProfileTabs("Following"),
                      props.setSelectedTabsStatus((e: boolean) => !e);
                  }}
                >
                  <p className="font-bold max-md:text-sm">
                    {props.profileQueryData?.accountStats?.graphFollowStats?.following || 0}
                  </p>
                  <p className="text-[#616770] -mt-[4px] max-md:-mt-[6px]  max-md:text-[12px]">
                    Following
                  </p>
                </div>{" "}
                <div
                  className=" cursor-pointer"
                  onClick={() => {
                    props.setSelectedTabs("Profiles"),
                      props.setSelectedProfileTabs("Followers"),
                      props.setSelectedTabsStatus((e: boolean) => !e);
                  }}
                >
                  <p className="font-bold max-md:text-sm">
                    {props.profileQueryData?.accountStats?.graphFollowStats?.followers || 0}
                  </p>
                  <p className="text-[#616770] -mt-[4px] max-md:-mt-[6px]  max-md:text-[12px]">
                    Followers
                  </p>
                </div>
              </div>

              <div className="flex flex-col justify-between items-end">
                {/* {!auth.isLogin && auth.isLoginLens && (
                  <Badge
                    className={
                      true
                        ? "btn-xs text-white w-20 cursor-not-allowed"
                        : "btn-xs text-white w-20"
                    }
                    onClick={() => {
                      setIsSigninOpenweb2(true);
                    }}
                  >
                    {false ? (
                      <span className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></span>
                    ) : (
                      "Login Amuzn"
                    )}
                  </Badge>
                )} */}

                <div className="flex flex-col items-center p-0">
                  <div className="rounded-full p-0 flex items-center border">
                    {/* AMuzn Button */}
                    {userId && (
                      <Link
                        href={`/profile/amuzn/${profile?.profileData?.profile_name?.replace(/\s+/g, "-")}`}
                      >
                        <button
                          onClick={() => setActiveIcon("amuzn")}
                          className={`flex items-center justify-center px-4 max-md:px-2 py-2 max-md:py-1 rounded-full transition-all duration-300 max-w-[55px] w-[55px] ${
                            activeIcon === "amuzn"
                              ? "grayscale-0 text-black border"
                              : "grayscale text-gray-600"
                          }`}
                          style={
                            activeIcon === "amuzn"
                              ? {
                                  backgroundColor: bgColor ? `${bgColor}30` : "rgba(0,0,0,0.05)",
                                }
                              : {}
                          }
                        >
                          <img
                            src="/assets/logo/Default.png"
                            alt=""
                            height={17}
                            className="items-center h-[17px] max-md:h-[15px] mix-blend-multiply"
                          />
                        </button>
                      </Link>
                    )}

                    {/* Lens Button */}
                    <button
                      onClick={() => setActiveIcon("lens")}
                      className={`flex items-center justify-center px-2 max-md:px-2 py-2 max-md:py-1 rounded-full transition-all duration-300 max-w-[55px] w-[55px] ${
                        activeIcon === "lens"
                          ? "grayscale-0 text-black border"
                          : "grayscale text-gray-600"
                      }`}
                      style={
                        activeIcon === "lens"
                          ? {
                              backgroundColor: bgColor ? `${bgColor}30` : "rgba(0,0,0,0.05)",
                            }
                          : {}
                      }
                    >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="28"
                        height="17"
                        fill={activeIcon === "lens" ? "black" : "currentColor"}
                        viewBox="0 0 28 17"
                      >
                        <g id="Group 3">
                          <path
                            id="Subtract"
                            fill={activeIcon === "lens" ? "black" : "currentColor"}
                            fillOpacity={activeIcon === "lens" ? "1" : "0.5"}
                            fillRule="evenodd"
                            d="M18.957 5.217a4.76 4.76 0 0 1 3.25-1.29 5.066 5.066 0 0 1 5.063 5.068c0 2.422-2.396 4.493-2.996 4.97-2.801 2.23-6.45 3.535-10.389 3.535s-7.588-1.305-10.39-3.536C2.9 13.488.5 11.414.5 8.994c0-2.799 2.267-5.067 5.062-5.067 1.26 0 2.39.493 3.25 1.29l.09-.044C9.098 2.561 11.222.5 13.884.5s4.786 2.06 4.984 4.673zm1.01 5.04c.386.384.661.848.815 1.355h.003a.41.41 0 0 1-.327.514.41.41 0 0 1-.456-.29 2.4 2.4 0 0 0-.606-1.004 2.44 2.44 0 0 0-1.736-.719q-.038 0-.075.004l-.075.003a1.076 1.076 0 1 1-1.45.586q-.072.06-.14.126c-.286.286-.49.63-.605 1.005a.407.407 0 0 1-.457.29.41.41 0 0 1-.327-.515c.154-.508.43-.97.814-1.356a3.25 3.25 0 0 1 2.312-.957c.871 0 1.695.34 2.31.957m-10-.137.075-.003.075-.004c.657 0 1.273.256 1.736.719.286.286.49.63.606 1.005a.41.41 0 0 0 .456.29.41.41 0 0 0 .327-.515 3.23 3.23 0 0 0-.814-1.356 3.25 3.25 0 0 0-2.311-.957c-.872 0-1.696.34-2.312.957-.384.385-.66.848-.813 1.356a.41.41 0 0 0 .327.514.407.407 0 0 0 .456-.29 2.43 2.43 0 0 1 .745-1.13 1.076 1.076 0 1 0 2.077.388c0-.432-.259-.803-.627-.974zm3.922 3.93a2.45 2.45 0 0 0 1.634-.626.415.415 0 0 1 .551-.007c.**************.01.6a3.25 3.25 0 0 1-4.391 0 .405.405 0 0 1 .01-.6.41.41 0 0 1 .552.007 2.44 2.44 0 0 0 1.633.627"
                            clipRule="evenodd"
                          ></path>
                        </g>
                      </svg>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        {
          <div>
            {address || isConnected ? (
              <div>
                {props.isOtherProfile ? (
                  <div>
                    {localFollowStatus ? (
                      <div className="row w-full gap-3">
                        <Badge
                          className={
                            auth.isLogin
                              ? "btn-xs h-[40px] w-full py-3 border-primary btn"
                              : "  btn-xs h-[40px] w-full py-3 border-primary btn opacity-50 pointer-events-none"
                          }
                          variant="outline"
                          style={{ fontSize: "16px" }}
                        >
                          Message
                        </Badge>

                        <Badge
                          className={`btn-xs font-normal font-sf border-primary btn min-w-20 w-20 ${
                            isLoading ? "opacity-70 pointer-events-none" : ""
                          }`}
                          variant="outline"
                          onClick={async () => {
                            if (isLoading) return; // Extra protection
                            try {
                              setIsLoading(true);
                              setCurrentStatus("unfollow");
                              const resp = await unfollow(props.profileQueryData?.account?.address);
                              // Update local state immediately
                              setLocalFollowStatus(false);
                              refetch();
                            } finally {
                              setIsLoading(false);
                            }
                          }}
                          style={{
                            fontSize: "14px",
                            height: "25px",
                            minHeight: "25px",
                            maxHeight: "25px",
                            width: "50%",
                            minWidth: "50%",
                            maxWidth: "50%",
                            backgroundColor: "transparent",
                            borderColor: "#333333",
                            borderWidth: "1px",
                            borderStyle: "solid",
                            borderRadius: "20px",
                            padding: "0",
                          }}
                        >
                          {isLoading ? (
                            <span className="w-4 h-4 border-2 border-gray-500 border-t-transparent rounded-full animate-spin"></span>
                          ) : (
                            "Unfollow"
                          )}
                        </Badge>
                      </div>
                    ) : (
                      <div className="row w-full gap-3">
                        <Badge
                          className={
                            auth.isLogin
                              ? "btn-xs h-[40px] w-full py-3 border-primary btn"
                              : "  btn-xs h-[40px] w-full py-3 border-primary btn opacity-50 pointer-events-none"
                          }
                          variant="outline"
                          style={{ fontSize: "16px" }}
                        >
                          Message
                        </Badge>

                        <Badge
                          className={`btn-xs text-white w-full ${
                            isLoading ? "opacity-70 pointer-events-none" : ""
                          }`}
                          onClick={async () => {
                            if (isLoading) return; // Extra protection
                            try {
                              setIsLoading(true);
                              setCurrentStatus("follow");
                              await followUser(props.profileQueryData?.account?.address);
                              // Update local state immediately
                              setLocalFollowStatus(true);
                              refetch();
                            } finally {
                              setIsLoading(false);
                            }
                          }}
                          style={{
                            fontSize: "14px",
                            height: "25px",
                            minHeight: "25px",
                            maxHeight: "25px",
                            width: "50%",
                            minWidth: "50%",
                            maxWidth: "50%",
                            backgroundColor: "#333333",
                            color: "white",
                            borderRadius: "20px",
                            padding: "0",
                          }}
                        >
                          {isLoading ? (
                            <span className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></span>
                          ) : (
                            "Follow"
                          )}
                        </Badge>
                      </div>
                    )}
                  </div>
                ) : (
                  !auth.isLogin && (
                    <div className="row w-full gap-3">
                      <Badge
                        className={
                          auth.isLogin
                            ? "btn-xs h-[40px] w-full py-3 border-primary btn"
                            : "  btn-xs h-[40px] w-full py-3 border-primary btn opacity-50 pointer-events-none"
                        }
                        variant="outline"
                        style={{ fontSize: "16px" }}
                      >
                        Message
                      </Badge>

                      <Badge
                        className="btn-xs text-white w-full"
                        onClick={async () => {
                          setIsSigninOpenweb2(true);
                        }}
                        style={{
                          fontSize: "14px",
                          height: "25px",
                          minHeight: "25px",
                          maxHeight: "25px",
                          width: "50%",
                          minWidth: "50%",
                          maxWidth: "50%",
                          backgroundColor: "#333333",
                          color: "white",
                          borderRadius: "20px",
                          padding: "0",
                        }}
                      >
                        Login Amuzn
                      </Badge>
                    </div>
                  )
                )}
              </div>
            ) : (
              <div className="grid grid-cols-1 w-full gap-3">
                <Badge
                  className="btn-xs text-white"
                  onClick={() => {
                    // Use the global sign-in store to open the wallet connect modal
                    useSignInStore.getState().setIsOpen(true);
                  }}
                >
                  Connect Wallet
                </Badge>
              </div>
            )}
          </div>
        }

        <div>
          <div className="mt-4">
            <div className="flex flex-row gap-4 items-start justify-between">
              <div>
                <div
                  className="text-primary max-md:text-sm"
                  onClick={() => {
                    // Only execute the toggle function if on mobile view (screen width <= 768px)
                    // Don't toggle when inside a modal or when an input is focused
                    if (
                      window.innerWidth <= 768 &&
                      !document.activeElement?.tagName.match(/input|textarea/i)
                    ) {
                      setIsToggle(!isToggle);
                    }
                  }}
                >
                  {feed ? (
                    <div
                      className={
                        isToggle
                          ? "text-primary line-clamp-2 whitespace-pre-line cursor-pointer"
                          : "text-primary whitespace-pre-line cursor-pointer"
                      }
                      onClick={(e) => {
                        // Only execute the toggle function if on mobile view (screen width <= 768px)
                        // Don't toggle when inside a modal or when an input is focused
                        if (
                          window.innerWidth <= 768 &&
                          !isOpen &&
                          !document.activeElement?.tagName.match(/input|textarea/i)
                        ) {
                          props.setIsToggle(!props.isToggle);
                        }
                      }}
                    >
                      <span className="font-bold max-md:text-sm text-nowrap">About me:</span> {feed}
                    </div>
                  ) : (
                    <span className="font-bold max-md:text-sm text-nowrap">About me*</span>
                  )}
                </div>
              </div>
              <div className="row gap-3">
                {!props.isOtherProfile && (address || isConnected) && (
                  <div onClick={() => setIsOpenAbout(true)} className=" cursor-pointer">
                    <Edit2 color={props.bgColor} />
                  </div>
                )}
                <Triangle
                  size={18}
                  className={
                    isToggle
                      ? "fill-primary rotate-180 cursor-pointer md:hidden"
                      : "fill-primary cursor-pointer md:hidden"
                  }
                  onClick={() => {
                    // Only toggle if no input is focused
                    if (!document.activeElement?.tagName.match(/input|textarea/i)) {
                      setIsToggle(!isToggle);
                    }
                  }}
                />
              </div>
            </div>
          </div>
        </div>

        {/* <ProfileInfoAbout
          isOtherProfile={props.isOtherProfile}
          bgColor={bgColor}
          isToggle={isToggle}
          setIsToggle={setIsToggle}
          about={feed || ""}
          setFeed={setFeed}
          onClickAction={handleSubmitUpdateFeed} // Pass function to child
        /> */}
        {!isToggle && profile?.profileData?.personal_moto && (
          <ProfileInfoPersonalMotto
            isOtherProfile={props.isOtherProfile}
            bgColor={bgColor}
            personalMotto={profile?.profileData?.personal_moto || ""}
          />
        )}
        {!isToggle && profile?.profileData?.hashtags?.length > 0 && (
          <ProfileInfoHashtags
            isOtherProfile={props.isOtherProfile}
            bgColor={bgColor}
            hashtags={profile?.profileData?.hashtags || ""}
          />
        )}
        {!isToggle &&
          (profile?.profileData?.facebookLink ||
            profile?.profileData?.instagramLink ||
            profile?.profileData?.twitterLink ||
            profile?.profileData?.websiteLink ||
            profile?.profileData?.youtubeLink) && (
            <ProfileInfoSocialMedia
              isOtherProfile={props.isOtherProfile}
              bgColor={bgColor}
              facebookLink={profile?.profileData?.facebookLink || ""}
              instagramLink={profile?.profileData?.instagramLink || ""}
              twitterLink={profile?.profileData?.twitterLink || ""}
              websiteLink={profile?.profileData?.websiteLink || ""}
              youtubeLink={profile?.profileData?.youtubeLink || ""}
            />
          )}
      </div>

      {/* About Modal */}
      <div>
        <Modal
          isDismissable={false}
          isOpen={isOpenAbout}
          placement="auto"
          onOpenChange={(open) => {
            // Only allow closing if not in loading state
            if (!isLoaading) {
              setIsOpenAbout(open);
            }
          }}
          hideCloseButton={true}
        >
          <ModalContent className="modal-content">
            {(onClose) => (
              <>
                <ModalBody>
                  {aboutSuccess ? (
                    <div className="flex flex-col items-center justify-center py-10">
                      <div className="bg-green-100 rounded-full p-4 mb-4">
                        <Check size={48} className="text-green-500" />
                      </div>
                      <h2 className="text-xl font-bold mb-2 text-center">
                        About Me Updated Successfully!
                      </h2>
                      <p className="text-gray-500 text-center">
                        Your profile information has been updated and the changes will be visible on
                        your profile.
                      </p>
                    </div>
                  ) : isLoaading ? (
                    <div className="flex flex-col items-center justify-center py-10">
                      <div className="bg-blue-50 rounded-full p-4 mb-4">
                        <Loader size={48} className="text-primary animate-spin" />
                      </div>
                      <h2 className="text-xl font-bold mb-2 text-center">Updating About Me...</h2>
                      <p className="text-gray-500 text-center">
                        Please wait while we update your profile information.
                      </p>
                    </div>
                  ) : (
                    <>
                      <div className="row justify-between">
                        <div
                          onClick={() => !isLoaading && setIsOpenAbout(false)}
                          className={
                            isLoaading ? "cursor-not-allowed opacity-50" : "cursor-pointer"
                          }
                        >
                          <X />
                        </div>
                        <p className="font-bold text-primary">Edit About Me </p>
                        <p
                          className={
                            feed && !isLoaading
                              ? "font-bold text-primary cursor-pointer"
                              : "font-bold text-borderColor cursor-not-allowed"
                          }
                          onClick={
                            feed && !isLoaading ? () => handleSubmitUpdateFeed("about") : undefined
                          }
                        >
                          {isLoaading ? "Saving..." : "Save"}
                        </p>
                      </div>

                      <div>
                        <div className="grid w-full items-center gap-1.5 mt-6">
                          <Label htmlFor="aboutMe" className="text-base font-[600] text-titleLabel">
                            About me*
                          </Label>
                          <Textarea
                            id="aboutMe"
                            placeholder="Tell the world about you and your services"
                            className="resize-none h-40 outline-none text-lg text-primary"
                            value={feed}
                            onChange={(e) => setFeed(e.target.value)}
                            disabled={isLoaading}
                          />
                        </div>
                      </div>
                    </>
                  )}
                </ModalBody>
              </>
            )}
          </ModalContent>
        </Modal>
      </div>

      {/* Report Post */}
      <div>
        <Modal
          isDismissable={false}
          isOpen={isResonPost}
          placement="auto"
          onOpenChange={setIsResonPost}
          hideCloseButton={true}
        >
          <ModalContent className="modal-content">
            {(onClose) => (
              <>
                <ModalBody>
                  <div className=" relative w-full">
                    <div className="row justify-between mb-5">
                      <X
                        onClick={() => setIsResonPost(false)}
                        className="text-primary cursor-pointer"
                      />
                      <p className="font-bold  text-titleLabel ">Report Post</p>
                      <p className=" opacity-0">Test</p>
                    </div>

                    <div className="min-w-full pb-5 ">
                      <div className="grid w-full gap-1.5 px-3 ">
                        <div className="grid w-full md:max-w-sm items-center gap-1 mt-4 max-md:text-start">
                          <Label
                            htmlFor="message"
                            className="text-primary text-base font-bold max-md:text-start"
                          >
                            Reason
                          </Label>
                          {resonData.map((item, indexs) => (
                            <div className="grid grid-cols-1 mt-1" key={indexs}>
                              <div className="row gap-3">
                                {isCheck[indexs] ? (
                                  <CheckCircle
                                    className="text-primary w-[18px] min-w-[18px] min-h-[18px] max-w-[18px] max-h-[18px] cursor-pointer"
                                    size={18}
                                    onClick={() => handleIsCheck(indexs)}
                                  />
                                ) : (
                                  <Circle
                                    className="text-subtitle w-[18px] min-w-[18px] min-h-[18px] max-w-[18px] max-h-[18px] cursor-pointer"
                                    // size={18}
                                    onClick={() => handleIsCheck(indexs)}
                                  />
                                )}
                                <p
                                  className={
                                    isCheck[indexs]
                                      ? "text-primary cursor-pointer"
                                      : "text-subtitle cursor-pointer"
                                  }
                                  onClick={() => handleIsCheck(indexs)}
                                >
                                  {item}
                                </p>
                              </div>
                            </div>
                          ))}
                        </div>
                        <div className="grid items-center gap-1.5 mt-4 max-md:text-start ">
                          <Label htmlFor="message" className="text-primary text-base font-bold">
                            Сomment
                          </Label>

                          <Textarea
                            placeholder="Сomment..."
                            id="message"
                            className="text-primary text-base w-full"
                            value={reportComment}
                            onChange={(e) => setReportComment(e.target.value)}
                          />
                        </div>
                        <Badge
                          className=" btn-xs btn text-center mt-5 ounded-full w-[150px] min-h-[30px]"
                          variant="outline"
                          onClick={async () => {
                            try {
                              if (!address) {
                                useSignInStore.getState().setIsOpen(true);
                                return;
                              }
                              const reasonKey = getReasonKey(reportRespon);

                              if (!reasonKey) {
                                throw new Error("Invalid reporting reason");
                              }

                              const resp = await reportPost({
                                request: {
                                  post: props.cardData.id,
                                  additionalComment: reportComment,
                                  reason: reportRespon as PostReportReason,
                                  // for: props.cardData.id,
                                  // additionalComments: reportComment,
                                  // reason: {
                                  //   [reasonKey]: {
                                  //     reason: reportRespon,
                                  //     subreason: reportingReasonMap[reportRespon],
                                  //   },
                                  // },
                                },
                              });

                              // console.log(resp);
                              if (resp) {
                                setIsResonPost(false);
                                alert("Report Post Done");
                              }
                            } catch (error) {
                              alert(error ?? "something went wrong Check Wallet Connect");
                            }
                          }}
                        >
                          Confirm
                        </Badge>
                      </div>
                    </div>
                  </div>
                </ModalBody>
              </>
            )}
          </ModalContent>
        </Modal>
      </div>

      {/* Edit Personal Info Modal */}
      <div>
        <Modal
          isDismissable={false}
          isOpen={isOpen}
          placement="auto"
          onOpenChange={(open) => {
            // Only allow closing if not in loading state
            if (!isLoaading) {
              setIsOpen(open);
            }
          }}
          hideCloseButton={true}
          size="2xl"
        >
          <ModalContent className="modal-content">
            {(onClose) => (
              <>
                <ModalBody>
                  {personalInfoSuccess ? (
                    <div className="flex flex-col items-center justify-center py-10">
                      <div className="bg-green-100 rounded-full p-4 mb-4">
                        <Check size={48} className="text-green-500" />
                      </div>
                      <h2 className="text-xl font-bold mb-2 text-center">
                        Personal Info Updated Successfully!
                      </h2>
                      <p className="text-gray-500 text-center">
                        Your profile information has been updated and the changes will be visible on
                        your profile.
                      </p>
                    </div>
                  ) : isLoaading ? (
                    <div className="flex flex-col items-center justify-center py-10">
                      <div className="bg-blue-50 rounded-full p-4 mb-4">
                        <Loader size={48} className="text-primary animate-spin" />
                      </div>
                      <h2 className="text-xl font-bold mb-2 text-center">
                        Updating Personal Info...
                      </h2>
                      <p className="text-gray-500 text-center">
                        Please wait while we update your profile information.
                      </p>
                    </div>
                  ) : (
                    <>
                      <div className="row justify-between">
                        <div
                          onClick={() => !isLoaading && setIsOpen(false)}
                          className={
                            isLoaading ? "cursor-not-allowed opacity-50" : "cursor-pointer"
                          }
                        >
                          <X />
                        </div>
                        <p className="font-bold text-primary">Edit Personal Info</p>
                        <p
                          className={
                            name && location && !isLoaading
                              ? "font-bold text-primary cursor-pointer"
                              : "font-bold text-borderColor cursor-not-allowed"
                          }
                          onClick={
                            name && location && !isLoaading
                              ? () => handleSubmitUpdateFeed("personalInfo")
                              : undefined
                          }
                        >
                          {isLoaading ? "Saving..." : "Save"}
                        </p>
                      </div>

                      <div>
                        <p className="mt-6 font-bold">Visible to all</p>
                        <div className="grid w-full items-center gap-1.5 mt-3">
                          <Label
                            htmlFor="profileName"
                            className="text-base font-[600] text-titleLabel"
                          >
                            Profile name*
                          </Label>
                          <Input
                            id="profileName"
                            placeholder="Profile name"
                            className="resize-none h-[40px] outline-none text-lg text-primary"
                            value={name}
                            onChange={(e) => setName(e.target.value)}
                            disabled={isLoaading}
                          />
                        </div>

                        <div className="grid w-full items-center gap-1.5 mt-3">
                          <Label
                            htmlFor="location"
                            className="text-base font-[600] text-titleLabel"
                          >
                            Location *
                          </Label>
                          <Input
                            id="location"
                            placeholder="Location"
                            className="resize-none h-[40px] outline-none text-lg text-primary"
                            value={location}
                            onChange={(e) => setLocation(e.target.value)}
                            disabled={isLoaading}
                          />
                        </div>
                      </div>
                    </>
                  )}
                </ModalBody>
              </>
            )}
          </ModalContent>
        </Modal>
      </div>

      {/* Delete Modal */}
      <div>
        <Modal
          isDismissable={false}
          isOpen={isOpenDelete}
          placement="auto"
          onOpenChange={setIsOpenDelete}
          hideCloseButton={true}
        >
          <ModalContent className="modal-content">
            {(onClose) => (
              <>
                <ModalBody>
                  <p className="text-center text-black text-lg">
                    Are you sure you want to delete Service #1?
                  </p>
                  <div className="px-12">
                    <Button
                      variant="outline"
                      className=" rounded-full w-full mt-5 border-black text-black border-2 py-5  text-base"
                      onClick={() => {
                        setIsOpenDelete(false), setIsOpenDeleteReason(true);
                      }}
                    >
                      Yes, delete
                    </Button>
                    <Button
                      variant="outline"
                      className=" rounded-full w-full mt-3 border-black text-black border-2 py-5 text-base "
                      onClick={() => setIsOpenDelete(false)}
                    >
                      No, cancel
                    </Button>
                  </div>
                </ModalBody>
              </>
            )}
          </ModalContent>
        </Modal>
      </div>

      {/* Delete open other Modal */}
      <div>
        <Modal
          isDismissable={false}
          isOpen={isOpenOtherDelete}
          placement="auto"
          onOpenChange={setIsOpenOtherDelete}
          hideCloseButton={true}
        >
          <ModalContent className="modal-content">
            {(onClose) => (
              <>
                <ModalBody>
                  <p className="text-center text-black text-lg">
                    You can not delete your profile while you have open orders.
                  </p>
                  <div className="px-12">
                    <Button
                      variant="outline"
                      className=" rounded-full w-full mt-5 border-black text-black border-2 py-5  text-base"
                      onClick={() => setIsOpenOtherDelete(false)}
                    >
                      Ок
                    </Button>
                  </div>
                </ModalBody>
              </>
            )}
          </ModalContent>
        </Modal>
      </div>

      {/* Delete Reson Modal */}
      <div>
        <Modal
          isDismissable={false}
          isOpen={isOpenDeleteReason}
          placement="auto"
          onOpenChange={setIsOpenDeleteReason}
          hideCloseButton={true}
          size="2xl"
        >
          <ModalContent className="modal-content">
            {(onClose) => (
              <>
                <ModalBody>
                  <div className="row justify-between">
                    <div onClick={() => setIsOpenDeleteReason(false)} className=" cursor-pointer">
                      <X />
                    </div>
                    <p className="font-bold text-primary">Delete Profile</p>
                    <p className={false ? "font-bold text-primary" : "font-bold text-borderColor"}>
                      Confirm
                    </p>
                  </div>

                  <div>
                    <p className="mt-3 text-[16px] text-primary">
                      Alex, we're sorry to see you go.
                    </p>
                    <p className="mt-2 text-primary">
                      Just a quick reminder, closing your account means you'll lose all info. Are
                      you sure you would like to delete your profile?
                    </p>
                    <div className="grid w-full items-center gap-1.5 mt-6">
                      <Label htmlFor="email" className="text-base font-[600] text-titleLabel">
                        Reason*
                      </Label>
                      <Textarea
                        placeholder="Please give your reason"
                        className="resize-none h-40 outline-none text-lg text-primary"
                        //   {...field}
                      />
                    </div>
                  </div>
                </ModalBody>
              </>
            )}
          </ModalContent>
        </Modal>
        <Modal
          isDismissable={false}
          isOpen={isOpenOtherDelete}
          placement="auto"
          onOpenChange={setIsOpenOtherDelete}
          hideCloseButton={true}
        >
          <ModalContent className="modal-content">
            {(onClose) => (
              <>
                <ModalBody>
                  <p className="text-center text-black text-lg">
                    You can not delete your profile while you have open orders.
                  </p>
                  <div className="px-12">
                    <Button
                      variant="outline"
                      className=" rounded-full w-full mt-5 border-black text-black border-2 py-5  text-base"
                      onClick={() => setIsOpenOtherDelete(false)}
                    >
                      Ок
                    </Button>
                  </div>
                </ModalBody>
              </>
            )}
          </ModalContent>
        </Modal>
      </div>

      {/* Change Categories Modal */}
      <div>
        <Modal
          isDismissable={false}
          isOpen={isOpenChangeCategories}
          placement="auto"
          onOpenChange={(open) => {
            // Only allow closing if not in saving state
            if (!isSavingCategories) {
              setIsOpenChangeCategories(open);
            }
          }}
          hideCloseButton={true}
          size="2xl"
        >
          <ModalContent className="modal-content py-3 max-w-md mx-auto">
            {(onClose) => (
              <>
                <ModalBody>
                  {categorySaveSuccess ? (
                    <div className="flex flex-col items-center justify-center py-10">
                      <div className="bg-green-100 rounded-full p-4 mb-4">
                        <Check size={48} className="text-green-500" />
                      </div>
                      <h2 className="text-xl font-bold mb-2 text-center">
                        Categories Updated Successfully!
                      </h2>
                      <p className="text-gray-500 text-center">
                        Your categories have been updated and the changes will be visible on your
                        profile.
                      </p>
                    </div>
                  ) : isSavingCategories ? (
                    <div className="flex flex-col items-center justify-center py-10">
                      <div className="bg-blue-50 rounded-full p-4 mb-4">
                        <Loader size={48} className="text-primary animate-spin" />
                      </div>
                      <h2 className="text-xl font-bold mb-2 text-center">Updating Categories...</h2>
                      <p className="text-gray-500 text-center">
                        Please wait while we update your profile categories.
                      </p>
                    </div>
                  ) : (
                    <>
                      <div className="flex items-center justify-between border-b pb-3 mb-2">
                        <div
                          onClick={() => !isSavingCategories && setIsOpenChangeCategories(false)}
                          className={`p-1.5 rounded-full hover:bg-gray-100 transition-colors ${
                            isSavingCategories ? "cursor-not-allowed opacity-50" : "cursor-pointer"
                          }`}
                        >
                          <X size={20} />
                        </div>
                        <h3 className="font-bold text-lg text-primary">Change Categories</h3>
                        <div
                          className={`p-1.5 rounded-full transition-colors ${
                            mainCat && secondCat && !isSavingCategories
                              ? "text-primary hover:bg-gray-100 cursor-pointer"
                              : "text-gray-300 cursor-not-allowed"
                          }`}
                          onClick={
                            mainCat && secondCat && !isSavingCategories
                              ? handleSubmitChangeCategory
                              : undefined
                          }
                        >
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="20"
                            height="20"
                            viewBox="0 0 24 24"
                            fill="none"
                            stroke="currentColor"
                            strokeWidth="2"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                          >
                            <path d="M20 6 9 17l-5-5" />
                          </svg>
                        </div>
                      </div>

                      <div>
                        <div className="grid w-full items-center gap-1.5 mt-4">
                          <Label
                            htmlFor="mainCategory"
                            className="text-base font-[600] text-titleLabel items-start flex"
                          >
                            Main Category*
                          </Label>
                          <Input
                            id="mainCategory"
                            placeholder="Main Category"
                            className="resize-none h-[40px] outline-none text-lg text-primary"
                            value={mainCat}
                            onChange={(e) => setMainCat(e.target.value)}
                            disabled={isSavingCategories}
                          />
                        </div>

                        <div className="grid w-full items-center gap-1.5 mt-3">
                          <Label
                            htmlFor="secondaryCategory"
                            className="text-base font-[600] text-titleLabel items-start flex"
                          >
                            Secondary Category*
                          </Label>
                          <Input
                            id="secondaryCategory"
                            placeholder="Secondary Category"
                            className="resize-none h-[40px] outline-none text-lg text-primary"
                            value={secondCat}
                            onChange={(e) => setSecondCat(e.target.value)}
                            disabled={isSavingCategories}
                          />
                        </div>

                        <p className="text-sm text-gray-500 mt-4 mb-2">
                          Select your main category:
                        </p>
                        <div className="my-2 px-1 max-h-[200px] overflow-y-auto">
                          {Category.map((item, index) => (
                            <div
                              key={index}
                              className={`py-3 px-3 mb-2 rounded-xl ${
                                mainCat === item.title
                                  ? "bg-primary cursor-pointer"
                                  : "bg-[#F2F2F2] cursor-pointer"
                              }`}
                              onClick={() => handleItemClickCategory(index)}
                            >
                              <div className="row gap-x-2">
                                <Check
                                  color={mainCat === item.title ? "white" : "#25282B"}
                                  strokeWidth="1.6px"
                                />
                                <p
                                  className={
                                    mainCat === item.title ? "text-white" : "text-titleLabel"
                                  }
                                >
                                  {item.title}
                                </p>
                              </div>
                            </div>
                          ))}
                        </div>

                        <p className="text-sm text-gray-500 mt-4 mb-2">
                          Select your secondary category:
                        </p>
                        <div className="my-2 px-1 max-h-[200px] overflow-y-auto">
                          {Category.map((item, index) => (
                            <div
                              key={`secondary-${index}`}
                              className={`py-3 px-3 mb-2 rounded-xl ${
                                secondCat === item.title
                                  ? "bg-primary cursor-pointer"
                                  : "bg-[#F2F2F2] cursor-pointer"
                              }`}
                              onClick={() => handleItemClickCategorySecond(index)}
                            >
                              <div className="row gap-x-2">
                                <Check
                                  color={secondCat === item.title ? "white" : "#25282B"}
                                  strokeWidth="1.6px"
                                />
                                <p
                                  className={
                                    secondCat === item.title ? "text-white" : "text-titleLabel"
                                  }
                                >
                                  {item.title}
                                </p>
                              </div>
                            </div>
                          ))}
                        </div>

                        <button
                          className={`w-full h-[48px] mt-6 rounded-xl font-medium transition-colors ${
                            mainCat && secondCat && !isSavingCategories
                              ? "bg-primary text-white hover:bg-primary/90"
                              : "bg-gray-200 text-gray-400 cursor-not-allowed"
                          }`}
                          onClick={handleSubmitChangeCategory}
                          disabled={!mainCat || !secondCat || isSavingCategories}
                        >
                          Save Categories
                        </button>
                      </div>
                    </>
                  )}
                </ModalBody>
              </>
            )}
          </ModalContent>
        </Modal>
      </div>

      {/* Edit Photo  Modal */}
      <div>
        <Modal
          isDismissable={false}
          isOpen={isOpenEditPhoto}
          placement="auto"
          onOpenChange={(open) => {
            // Only allow closing if not in loading state
            if (!isLoaading) {
              setIsOpenEditPhoto(open);
            }
          }}
          hideCloseButton={true}
          size="2xl"
        >
          <ModalContent className="modal-content">
            {(onClose) => (
              <>
                <ModalBody>
                  {photoSuccess ? (
                    <div className="flex flex-col items-center justify-center py-10">
                      <div className="bg-green-100 rounded-full p-4 mb-4">
                        <Check size={48} className="text-green-500" />
                      </div>
                      <h2 className="text-xl font-bold mb-2 text-center">
                        Profile Photo Updated Successfully!
                      </h2>
                      <p className="text-gray-500 text-center">
                        Your profile photo has been updated and the changes will be visible on your
                        profile.
                      </p>
                    </div>
                  ) : isLoaading ? (
                    <div className="flex flex-col items-center justify-center py-10">
                      <div className="bg-blue-50 rounded-full p-4 mb-4">
                        <Loader size={48} className="text-primary animate-spin" />
                      </div>
                      <h2 className="text-xl font-bold mb-2 text-center">
                        Updating Profile Photo...
                      </h2>
                      <p className="text-gray-500 text-center">
                        Please wait while we update your profile photo.
                      </p>
                    </div>
                  ) : (
                    <>
                      <div className="row justify-between">
                        <div
                          onClick={() => {
                            if (!isLoaading) {
                              setIsOpenEditPhoto(false);
                              setMedia(null);
                            }
                          }}
                          className={
                            isLoaading ? "cursor-not-allowed opacity-50" : "cursor-pointer"
                          }
                        >
                          <X />
                        </div>
                        <p className="font-bold text-primary">Edit Photo</p>
                        <p
                          className={
                            isMedia && !isLoaading
                              ? "font-bold text-primary cursor-pointer"
                              : "font-bold text-borderColor cursor-not-allowed"
                          }
                          onClick={() => !isLoaading && isMedia && handleSubmitUpdateFeed("photo")}
                        >
                          {isLoaading ? "Saving..." : "Save"}
                        </p>
                      </div>
                      <div className="px-12 max-md:px-4">
                        {media && media instanceof Blob ? (
                          <div className="row justify-center mt-6 mb-4">
                            <img
                              src={URL.createObjectURL(media)}
                              alt="Media Preview"
                              className="w-[200px] h-[200px] rounded-full object-cover"
                            />
                          </div>
                        ) : (
                          <div className="row justify-center mt-6 mb-4">
                            <img
                              src={
                                props.profileQueryData?.account?.metadata?.picture ||
                                "https://static.hey.xyz/images/default.png"
                              }
                              alt=""
                              className="w-[200px] h-[200px] rounded-full object-cover"
                            />
                          </div>
                        )}

                        <label
                          htmlFor="media-upload"
                          className={`row gap-4 ${
                            isLoaading ? "cursor-not-allowed opacity-50" : "cursor-pointer"
                          }`}
                        >
                          <Badge
                            className={`btn-xs h-[40px] py-4 border-primary w-full ${
                              isLoaading ? "opacity-50 pointer-events-none" : ""
                            }`}
                            variant="outline"
                            style={{ fontSize: "16px" }}
                          >
                            Upload a Photo
                          </Badge>
                          <input
                            type="file"
                            id="media-upload"
                            className="hidden"
                            accept="image/*"
                            onChange={handleMediaUpload}
                            disabled={isLoaading}
                          />
                        </label>
                        <p className="mt-5 text-lg text-center">
                          Please upload a headshot photo, as required for all content providers.
                        </p>
                      </div>
                    </>
                  )}
                </ModalBody>
              </>
            )}
          </ModalContent>
        </Modal>
      </div>

      {/* view Photo  Modal */}
      <div>
        <Modal
          isDismissable={false}
          isOpen={isOpenViewPhoto}
          placement="auto"
          onOpenChange={setIsOpenViewPhoto}
          hideCloseButton={true}
        >
          <ModalContent className="modal-content">
            {(onClose) => (
              <>
                <ModalBody>
                  <div className="row justify-between">
                    <div
                      onClick={() => {
                        setIsOpenViewPhoto(false), setMedia(null);
                      }}
                      className=" cursor-pointer"
                    >
                      <X />
                    </div>
                  </div>
                  <div className="px-12 max-md:px-4">
                    {media && media instanceof Blob ? (
                      <div className="row justify-center mt-6 mb-4">
                        <img
                          src={URL.createObjectURL(media)}
                          alt="Media Preview"
                          className="w-[200px] h-[200px] rounded-full object-cover"
                        />
                      </div>
                    ) : (
                      <div className="row justify-center mt-6 mb-4">
                        <img
                          src={
                            props.profileQueryData?.account?.metadata?.picture ||
                            "https://static.hey.xyz/images/default.png"
                          }
                          alt=""
                          className="w-[300px] h-[300px]"
                        />
                      </div>
                    )}
                  </div>
                </ModalBody>
              </>
            )}
          </ModalContent>
        </Modal>
      </div>

      {/* Bottom Icon  */}

      <Sheet open={isOpenSheet} onOpenChange={setIsOpenSheet}>
        <SheetContentNoClose side="bottom" className="bg-transparent border-none p-0">
          <div className="bg-white rounded-[14px]">
            <button
              className="bg-white text-primary w-full h-[50px] rounded-tr-[14px]  rounded-tl-[14px] border-b"
              onClick={() => {
                setIsOpenSheet(false);
                setIsOpenShare(true);
              }}
            >
              Share Profile
            </button>
            <button
              className="bg-white text-primary w-full h-[50px] rounded-[14px]"
              // onClick={() => setIsOpenDelete(true)}
              onClick={() => {
                address ? setIsResonPost(true) : setIsOpenSheet(false),
                  useSignInStore.getState().setIsOpen(true);
              }}
            >
              Report Profile
            </button>
          </div>
          <div className="mt-4">
            <button
              className="bg-white text-primary w-full h-[50px] rounded-[14px] "
              onClick={() => setIsOpenSheet(false)}
            >
              Cancel
            </button>
          </div>
        </SheetContentNoClose>
      </Sheet>

      <div className="max-md:h-full px-5">
        <AlertDialog
          open={isSigninOpen}
          onOpenChange={(val: any) => {
            console.log(val, "valvalvalvalval");
            // setIsSigninOpen(val)
          }}
        >
          <AlertDialogTrigger asChild></AlertDialogTrigger>
          <AlertDialogContent className="py-10 px-28 max-md:px-8 md:rounded-xl  max-md:h-full max-md:w-full max-md:-mt-[1px] max-md:overflow-scroll">
            <AlertDialogHeader>
              <AlertDialogDescription className=" max-md: overflow-scroll h-full hide-scroll ">
                <div
                  className="absolute top-6 left-6 cursor-pointer"
                  onClick={() => {
                    sessionStorage.removeItem("input");
                    sessionStorage.removeItem("openPost");
                    setIsSigninOpen(false);
                  }}
                >
                  <X />
                </div>
                <SignInButton />
              </AlertDialogDescription>
            </AlertDialogHeader>
          </AlertDialogContent>
        </AlertDialog>
      </div>
      <div className="max-md:h-full px-5">
        <AlertDialog
          open={isSigninOpenweb2}
          onOpenChange={(val: any) => {
            console.log(val, "valvalvalvalval");
            // setIsSigninOpen(val)
          }}
        >
          <AlertDialogTrigger asChild></AlertDialogTrigger>
          <AlertDialogContent className="py-10 px-28 max-md:px-8 md:rounded-xl  max-md:h-full max-md:w-full max-md:-mt-[1px] max-md:overflow-scroll">
            <AlertDialogHeader>
              <AlertDialogDescription className=" max-md: overflow-scroll h-full hide-scroll ">
                <div
                  className="absolute top-6 left-6 cursor-pointer"
                  onClick={() => {
                    // sessionStorage.removeItem("input");
                    // sessionStorage.removeItem("openPost");
                    setIsSigninOpenweb2(false);
                  }}
                >
                  <X />
                </div>
                <AuthSignup onClose={() => setIsSigninOpenweb2(false)} />
              </AlertDialogDescription>
            </AlertDialogHeader>
          </AlertDialogContent>
        </AlertDialog>
      </div>

      {/* Share Profile Modal */}
      <div>
        <Modal
          isDismissable={false}
          isOpen={isOpenShare}
          placement="auto"
          onOpenChange={setIsOpenShare}
          hideCloseButton={true}
          size="2xl"
        >
          <ModalContent className="modal-content">
            {() => (
              <>
                <ModalBody>
                  <div>
                    <div>
                      <div className="flex items-center justify-between">
                        <div
                          onClick={() => setIsOpenShare(false)}
                          className="p-1.5 rounded-full hover:bg-gray-100 cursor-pointer transition-colors"
                        >
                          <X size={20} />
                        </div>
                        <h3 className="font-bold text-lg text-primary">Share Profile</h3>
                        <div className="w-8"></div>
                      </div>

                      <div className="mt-6">
                        <div className="flex items-center justify-between border border-gray-200 rounded-lg p-3 bg-gray-50">
                          <div className="truncate mr-2 text-gray-700">
                            https://www.amuzn.app/profile/lens/
                            {props.profileQueryData?.account?.username?.localName}
                          </div>
                          <div
                            className={`cursor-pointer p-2 hover:bg-gray-100 rounded-full transition-all ${
                              isCopied ? "bg-green-100 text-green-600" : ""
                            }`}
                            onClick={() =>
                              copyProfileLink(props.profileQueryData?.account?.username?.localName)
                            }
                          >
                            {isCopied ? <Check size={18} /> : <Copy size={18} />}
                          </div>
                        </div>

                        <p className="text-gray-500 text-sm mt-4 mb-2">Share on social media</p>

                        <div className="grid grid-cols-4 gap-3 mt-2">
                          <a
                            href={`https://twitter.com/intent/tweet?url=${encodeURIComponent(
                              `https://www.amuzn.app/profile/${props?.otherUserID}`
                            )}&text=${encodeURIComponent("Check out this profile on Amuzn!")}`}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="flex flex-col items-center justify-center"
                          >
                            <div className="bg-[#EEEEEE] p-3 rounded-full w-fit mb-1">
                              <Twitter size={24} className="fill-primary border-0 outline-0" />
                            </div>
                            <span className="text-xs">Twitter</span>
                          </a>

                          <a
                            href={`https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(
                              `https://www.amuzn.app/profile/${props?.otherUserID}`
                            )}`}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="flex flex-col items-center justify-center"
                          >
                            <div className="bg-[#EEEEEE] p-3 rounded-full w-fit mb-1">
                              <Facebook size={24} className="fill-primary border-0 outline-0" />
                            </div>
                            <span className="text-xs">Facebook</span>
                          </a>

                          <div
                            onClick={() => {
                              // Instagram doesn't have a direct share URL, so we'll copy the link to clipboard
                              const profileUrl = `https://www.amuzn.app/profile/${props?.otherUserID}`;
                              navigator.clipboard.writeText(profileUrl);
                              setIsCopied(true);
                              setTimeout(() => setIsCopied(false), 2000);
                              // Open Instagram in a new tab
                              window.open("https://www.instagram.com/", "_blank");
                            }}
                            className="flex flex-col items-center justify-center cursor-pointer"
                          >
                            <div className="bg-[#EEEEEE] p-3 rounded-full w-fit mb-1">
                              <Instagram size={24} className="border-0 outline-0" />
                            </div>
                            <span className="text-xs">Instagram</span>
                          </div>

                          {/* WhatsApp share button (replaces Telegram) */}
                          <a
                            href={`https://wa.me/?text=${encodeURIComponent(
                              `Check out this profile on Amuzn! https://www.amuzn.app/profile/${props?.otherUserID}`
                            )}`}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="flex flex-col items-center justify-center"
                          >
                            <div className="bg-[#EEEEEE] p-3 rounded-full w-fit mb-1">
                              {/* WhatsApp SVG icon */}
                              <svg
                                xmlns="http://www.w3.org/2000/svg"
                                width="24"
                                height="24"
                                viewBox="0 0 24 24"
                                fill="none"
                                className="fill-primary"
                              >
                                <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.472-.148-.67.15-.198.297-.767.966-.94 1.164-.173.198-.347.223-.644.075-.297-.149-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.372-.025-.521-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.007-.372-.009-.571-.009-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.099 3.2 5.077 4.363.71.306 1.263.489 1.694.625.712.227 1.36.195 1.872.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.288.173-1.413-.074-.124-.272-.198-.57-.347z" />
                                <path d="M12.004 2.003c-5.514 0-9.997 4.483-9.997 9.997 0 1.762.463 3.484 1.341 4.997l-1.409 5.151 5.283-1.389c1.475.807 3.153 1.238 4.782 1.238h.001c5.514 0 9.997-4.483 9.997-9.997 0-2.669-1.04-5.178-2.929-7.067-1.889-1.889-4.398-2.93-7.069-2.93zm0 17.995c-1.462 0-2.892-.393-4.13-1.137l-.295-.174-3.134.823.837-3.057-.192-.314c-.822-1.346-1.257-2.899-1.257-4.486 0-4.411 3.588-7.999 7.999-7.999 2.137 0 4.146.833 5.656 2.344 1.511 1.511 2.344 3.52 2.344 5.656 0 4.411-3.588 7.999-7.999 7.999z" />
                              </svg>
                            </div>
                            <span className="text-xs">WhatsApp</span>
                          </a>
                        </div>
                      </div>
                    </div>
                  </div>
                </ModalBody>
              </>
            )}
          </ModalContent>
        </Modal>
      </div>
    </>
  );
};

export default ProfileInfo;
