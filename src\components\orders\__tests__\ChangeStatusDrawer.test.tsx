/**
 * Test file for ChangeStatusDrawer iOS fixes and scrolling behavior
 */

import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import "@testing-library/jest-dom";
import ChangeStatusDrawer from "../ChangeStatusDrawer";

// Mock the iOS utility functions
jest.mock("@/utils/iosUtils", () => ({
  useIOSTextareaFix: () => ({
    handleFocus: jest.fn(),
    handleBlur: jest.fn(),
    cleanup: jest.fn(),
    isIOS: false,
  }),
  getIOSInputStyles: () => ({
    fontSize: "16px",
    WebkitAppearance: "none",
    borderRadius: "8px",
    WebkitTransform: "translateZ(0)",
    transform: "translateZ(0)",
  }),
}));

// Mock the services
jest.mock("@/services/ordersServices", () => ({
  UpdateActivityLog: jest.fn(),
}));

const defaultProps = {
  isOpen: true,
  onOpenChange: jest.fn(),
  currentStatus: "new",
  onStatusChange: jest.fn(),
  orderType: "received" as const,
  orderId: "test-order-id",
  loggedInUser: "test-user",
  sellerName: "test-seller",
  userName: "test-username",
};

describe("ChangeStatusDrawer Scrolling and Layout", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("renders the textarea with iOS-specific classes and styles", () => {
    render(<ChangeStatusDrawer {...defaultProps} />);

    const textarea = screen.getByPlaceholderText("Your comment");
    expect(textarea).toBeInTheDocument();
    expect(textarea).toHaveClass("ios-textarea-fix");
  });

  it("renders footer with sticky positioning for accessibility", () => {
    render(<ChangeStatusDrawer {...defaultProps} />);

    const confirmButton = screen.getByText("Confirm");
    const footer = confirmButton.closest(".sticky");

    expect(footer).toBeInTheDocument();
    expect(footer).toHaveClass("sticky", "bottom-0", "z-10");
  });

  it("renders content area with overflow-y-auto for scrolling", () => {
    render(<ChangeStatusDrawer {...defaultProps} />);

    // Check if the content area has scrolling enabled
    const contentArea = document.querySelector(".overflow-y-auto");
    expect(contentArea).toBeInTheDocument();
  });

  it("maintains layout structure for proper scrolling", () => {
    render(<ChangeStatusDrawer {...defaultProps} />);

    // Check if the main container has flex layout
    const mainContainer = document.querySelector(".flex.flex-col.h-full");
    expect(mainContainer).toBeInTheDocument();

    // Check if content area is flex-1 (takes available space)
    const contentArea = document.querySelector(".flex-1");
    expect(contentArea).toBeInTheDocument();
  });

  it("renders with proper drawer body class for iOS targeting", () => {
    render(<ChangeStatusDrawer {...defaultProps} />);

    // Check if the drawer body has the proper class for iOS targeting
    const drawerBody = document.querySelector(".drawer-body");
    expect(drawerBody).toBeInTheDocument();
  });

  it("handles textarea focus and blur events for iOS", () => {
    render(<ChangeStatusDrawer {...defaultProps} />);

    const textarea = screen.getByPlaceholderText("Your comment");

    // Test focus and blur events
    fireEvent.focus(textarea);
    fireEvent.blur(textarea);

    // Verify textarea is still accessible
    expect(textarea).toBeInTheDocument();
  });

  it("ensures confirm button is always accessible", () => {
    render(<ChangeStatusDrawer {...defaultProps} />);

    const confirmButton = screen.getByText("Confirm");
    expect(confirmButton).toBeInTheDocument();

    // Check if button is in a sticky footer
    const footer = confirmButton.closest(".sticky.bottom-0");
    expect(footer).toBeInTheDocument();
  });

  it("handles smooth transitions during focus/blur cycles", () => {
    render(<ChangeStatusDrawer {...defaultProps} />);

    const textarea = screen.getByPlaceholderText("Your comment");

    // Test multiple focus/blur cycles to ensure smooth transitions
    fireEvent.focus(textarea);
    fireEvent.blur(textarea);
    fireEvent.focus(textarea);
    fireEvent.blur(textarea);

    // Verify textarea is still accessible and functional
    expect(textarea).toBeInTheDocument();
    expect(textarea).not.toHaveAttribute("disabled");
  });

  it("applies CSS transitions for smooth layout changes", () => {
    render(<ChangeStatusDrawer {...defaultProps} />);

    // Check if drawer elements have transition classes
    const drawerBody = document.querySelector(".drawer-body");
    const drawerHeader = document.querySelector(".drawer-header");

    expect(drawerBody).toBeInTheDocument();
    expect(drawerHeader).toBeInTheDocument();

    // These elements should have CSS transitions applied via global styles
    const drawerBodyStyles = window.getComputedStyle(drawerBody as Element);
    expect(drawerBodyStyles.transition).toBeDefined();
  });
});
