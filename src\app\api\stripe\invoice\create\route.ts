import { NextRequest, NextResponse } from 'next/server';
import { getStripeInstance, type StripeInvoiceRequest } from '@/lib/stripe';

export async function POST(request: NextRequest) {
  try {
    const { customerId, isUS }: StripeInvoiceRequest = await request.json();

    if (!customerId) {
      return NextResponse.json({ error: 'Customer ID is required' }, { status: 400 });
    }

    const stripeService = getStripeInstance(isUS === 'true');

    console.log('[stripe/invoice/create] Routing', { region: isUS === 'true' ? 'us' : 'row', customerId });


    const invoice = await stripeService.invoices.create({
      customer: customerId
    });

    return NextResponse.json({
      invoice,
      success: true,
    });

  } catch (error) {
    console.error('Error creating invoice:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    return NextResponse.json({
      success: false,
      error: errorMessage
    }, { status: 500 });
  }
}
