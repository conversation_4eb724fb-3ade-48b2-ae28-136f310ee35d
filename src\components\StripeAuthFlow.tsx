"use client";

import React, { useState, useEffect } from "react";
import { useCurrentUser } from "@/hooks/useCurrentUser";
import { ChangeIsUsFlag } from "@/services/usersServices";
import { logIn as firebaseLogIn, signUp as firebaseSignUp } from "@/firebase/authService";
import { ConnectEmbeddedProvider } from "./ConnectEmbeddedProvider";
import ConnectDashboard from "./ConnectDashboard";
import { Button } from "./ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "./ui/card";
import { Input } from "./ui/input";
import { Label } from "./ui/label";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "./ui/tabs";
import {
  User,
  Mail,
  Lock,
  Eye,
  EyeOff,
  Loader2,
  AlertCircle,
  CheckCircle,
  CreditCard,
  Building,
} from "lucide-react";
import { Mo<PERSON>, <PERSON>dal<PERSON>ody, ModalContent } from "@heroui/react";


interface SellerAccount {
  stripeAccountId: string;
  onboardingComplete: boolean;
  chargesEnabled: boolean;
  payoutsEnabled: boolean;
  email?: string;
  businessName?: string;
}

export const StripeAuthFlow: React.FC = () => {
  const { user, loading: userLoading, isAuthenticated } = useCurrentUser();
  const [authMode, setAuthMode] = useState<"login" | "signup">("login");
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [sellerAccount, setSellerAccount] = useState<SellerAccount | null>(null);
  const [connectMode, setConnectMode] = useState<"create" | "oauth">("create");
  // Region selection dialog state/helpers
  const [regionPromptOpen, setRegionPromptOpen] = useState(false);
  const regionResolveRef = React.useRef<((v: boolean) => void) | null>(null);
  const regionRejectRef = React.useRef<((reason?: any) => void) | null>(null);

  const askIsUS = React.useCallback(() => {
    return new Promise<boolean>((resolve, reject) => {
      regionResolveRef.current = resolve;
      regionRejectRef.current = reject;
      setRegionPromptOpen(true);
    });
  }, []);

  const handleUS = React.useCallback(() => {
    setRegionPromptOpen(false);
    regionResolveRef.current?.(true);
    regionResolveRef.current = null;
    regionRejectRef.current = null;
  }, []);

  const handleROW = React.useCallback(() => {
    setRegionPromptOpen(false);
    regionResolveRef.current?.(false);
    regionResolveRef.current = null;
    regionRejectRef.current = null;
  }, []);

  const handleRegionDialogOpenChange = React.useCallback((open: boolean) => {
    setRegionPromptOpen(open);
    if (!open && regionResolveRef.current) {
      // User dismissed via close/X; treat as cancellation
      regionRejectRef.current?.(new Error("region_selection_cancelled"));
      regionResolveRef.current = null;
      regionRejectRef.current = null;
    }
  }, []);

  const RegionDialog: React.FC = () => (
    <Modal isOpen={regionPromptOpen} onOpenChange={handleRegionDialogOpenChange}>
      <ModalContent>
        {() => (
          <>
            <ModalBody>
              <h3 className="text-lg font-semibold">Choose Your Location</h3>
              <p className="text-sm text-muted-foreground">
                To continue setting up your Stripe account, please let us know where you’re located. This helps us give you the correct setup experience.
              </p>
            </ModalBody>
            <div className="flex items-center justify-end gap-2 px-6 pb-6">
              <Button variant="outline" onClick={handleROW}>I'm outside the US</Button>
              <Button onClick={handleUS}>Yes, I'm in the US</Button>
            </div>
          </>
        )}
      </ModalContent>
    </Modal>
  );

  const [dashboardLoading, setDashboardLoading] = useState(false);

  // Handle success message after returning from Stripe OAuth
  useEffect(() => {
    try {
      const url = new URL(window.location.href);
      const connected = url.searchParams.get("connected");
      const account = url.searchParams.get("account");
      const tab = url.searchParams.get("tab");
      if (connected === "true") {
        setSuccess("Connected successfully!");
        // Clean up query params so message doesn't persist on refresh
        url.searchParams.delete("connected");
        if (account) url.searchParams.delete("account");
        if (tab) url.searchParams.delete("tab");
        window.history.replaceState({}, "", url.toString());
      }
    } catch {}
  }, []);
  // Form data
  const [formData, setFormData] = useState({
    email: "",
    password: "",
    confirmPassword: "",
    displayName: "",
    businessName: "",
  });

  // Check for existing seller account when user is authenticated
  useEffect(() => {
    const checkSellerAccount = async () => {
      if (!user?.uid) return;

      try {
        const response = await fetch(`/api/sellers/${user.uid}`);
        if (response.ok) {
          const data = await response.json();
          setSellerAccount(data);
        }
      } catch (err) {
        console.error("Error checking seller account:", err);
      }
    };

    if (isAuthenticated) {
      checkSellerAccount();
    }
  }, [user?.uid, isAuthenticated]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
    setError(null);
  };

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError(null);

    try {
      const user = await firebaseLogIn(formData.email, formData.password);

      if (!user?.uid) {
        throw new Error("Login failed");
      }

      setSuccess("Login successful! Checking your seller account...");

      // Trigger seller account check
      const sellerResponse = await fetch(`/api/sellers/${user.uid}`);
      if (sellerResponse.ok) {
        const sellerData = await sellerResponse.json();
        setSellerAccount(sellerData);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "Login failed");
    } finally {
      setIsLoading(false);
    }
  };

  const handleSignup = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError(null);

    // Validation
    if (formData.password !== formData.confirmPassword) {
      setError("Passwords do not match");
      setIsLoading(false);
      return;
    }

    if (formData.password.length < 6) {
      setError("Password must be at least 6 characters");
      setIsLoading(false);
      return;
    }

    try {
      // 1. Create user account
      // Create Firebase user
      const createdUser = await firebaseSignUp(formData.email, formData.password);
      if (!createdUser?.uid) {
        throw new Error("Signup failed");
      }

      setSuccess("Account created successfully! Creating your Stripe seller account...");

      // 2. Ask region and create Stripe Connect account
      let isUS: boolean;
      try {
        isUS = await askIsUS();
      } catch {
        // User cancelled region selection
        setIsLoading(false);
        return;
      }
      try {
        await ChangeIsUsFlag({ user_id: createdUser.uid, isUs: isUS });
      } catch (e) {
        console.warn("Failed to update isUS flag", e);
      }
      const stripeResponse = await fetch("/api/connect/onboard", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          userId: createdUser.uid,
          email: formData.email,
          businessName: formData.businessName || formData.displayName,
          isUS,
        }),
      });

      const stripeData = await stripeResponse.json();

      if (!stripeResponse.ok) {
        throw new Error(stripeData.error || "Failed to create Stripe account");
      }

      setSuccess("Account and Stripe seller account created successfully!");

      // Fetch the complete seller account data
      const sellerResponse = await fetch(`/api/sellers/${createdUser.uid}`);
      if (sellerResponse.ok) {
        const sellerData = await sellerResponse.json();
        setSellerAccount(sellerData);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "Signup failed");
    } finally {
      setIsLoading(false);
    }
  };

  const handleCreateSellerAccount = async () => {
    if (!user?.uid || !user?.email) {
      setError("User information is required");
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      let isUS: boolean;
      try {
        isUS = await askIsUS();
      } catch {
        setIsLoading(false);
        return;
      }
      try {
        await ChangeIsUsFlag({ user_id: user.uid, isUs: isUS });
      } catch (e) {
        console.warn("Failed to update isUS flag", e);
      }
      const response = await fetch("/api/connect/onboard", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          userId: user.uid,
          email: user.email,
          businessName: user.displayName || user.email,
          isUS,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Failed to create Stripe account");
      }

      setSuccess("Stripe seller account created successfully!");

      // Fetch the seller account data
      const sellerResponse = await fetch(`/api/sellers/${user.uid}`);
      if (sellerResponse.ok) {
        const sellerData = await sellerResponse.json();
        setSellerAccount(sellerData);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to create seller account");
    } finally {
      setIsLoading(false);
    }
  };

  // Loading state
  if (userLoading) {
    return (
      <div className=" flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardContent className="flex items-center justify-center p-8">
            <div className="text-center">
              <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
              <p className="text-gray-600">Loading...</p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // User is authenticated and has seller account - show dashboard
  if (isAuthenticated && sellerAccount) {
    return (
      <div>
        <RegionDialog />

        <div className="container mx-auto px-2 py-2">
          <div>
            <div>
              <h1 className="text-3xl font-bold text-gray-900 leading-tight">
                <span className="block">Welcome back,</span>
                <span className="text-2xl max-md:text-lg">{user?.displayName || user?.email}</span>
              </h1>
              <p className="text-gray-600 mt-2">Manage your Stripe seller account</p>

              {/* <div className="flex flex-wrap items-center gap-2 mt-3">
                {/* <div
                  className={`px-3 py-1 rounded-full text-xs font-medium ${
                    sellerAccount.onboardingComplete
                      ? "bg-emerald-100 text-emerald-800"
                      : "bg-yellow-100 text-yellow-800"
                  }`}
                >
                  {sellerAccount.onboardingComplete ? "Active" : "Setup Required"}
                </div>
                <div
                  className={`px-3 py-1 rounded-full text-xs font-medium ${
                    sellerAccount.chargesEnabled
                      ? "bg-emerald-100 text-emerald-800"
                      : "bg-yellow-100 text-yellow-800"
                  }`}
                >
                  {sellerAccount.chargesEnabled ? "Charges Enabled" : "Charges Disabled"}
                </div>
                <div
                  className={`px-3 py-1 rounded-full text-xs font-medium ${
                    sellerAccount.payoutsEnabled
                      ? "bg-emerald-100 text-emerald-800"
                      : "bg-yellow-100 text-yellow-800"
                  }`}
                >
                  {sellerAccount.payoutsEnabled ? "Payouts Enabled" : "Payouts Disabled"}
                </div> */}
              {/* </div> */}

              <div className="mt-6">
                <Button
                  variant="secondary"
                  size="sm"
                  className="bg-gray-100 text-gray-800 hover:bg-gray-200"
                  onClick={async () => {
                    try {
                      setDashboardLoading(true);
                      const res = await fetch("/api/connect/login-link", {
                        method: "POST",
                        headers: { "Content-Type": "application/json" },
                        body: JSON.stringify({
                          accountId: sellerAccount?.stripeAccountId,
                          userId: user?.uid,
                        }),
                      });
                      const data = await res.json();
                      if (!res.ok || !data.loginUrl) {
                        throw new Error(data.error || "Failed to create login link");
                      }
                      window.open(data.loginUrl, "_blank");
                    } catch (err) {
                      setError(
                        err instanceof Error ? err.message : "Failed to open Stripe dashboard"
                      );
                    } finally {
                      setDashboardLoading(false);
                    }
                  }}
                >
                  {dashboardLoading ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Opening Stripe Dashboard...
                    </>
                  ) : (
                    "Open Stripe Dashboard"
                  )}
                </Button>

                {/*
                <Button
                  variant="outline"
                  size="sm"
                  className="ml-2"
                  onClick={() => {
                    window.location.href = '/payment/connect-dashboard';
                  }}
                >
                  Manage in Embedded Dashboard
                </Button>
                */}
              </div>
            </div>
          </div>

          {/* Hosted-only mode: hide embedded components */}
          {!sellerAccount.onboardingComplete && (
            <div className="mt-2">
              <Button
                size="sm"
                onClick={async () => {
                  try {
                    setDashboardLoading(true);
                    // If the user already has a Stripe account, request appropriate link
                    if (sellerAccount?.stripeAccountId) {
                      const res = await fetch("/api/connect/login-link", {
                        method: "POST",
                        headers: { "Content-Type": "application/json" },
                        body: JSON.stringify({
                          accountId: sellerAccount.stripeAccountId,
                          userId: user?.uid,
                        }),
                      });
                      const data = await res.json();
                      if (res.ok && data.loginUrl) {
                        window.location.href = data.loginUrl;
                        return;
                      }
                      // If Standard account (no Express dashboard), use OAuth
                      if (res.status === 400 && data?.oauthEndpoint === "/api/connect/login-oauth") {
                        const oauth = await fetch("/api/connect/login-oauth", {
                          method: "POST",
                          headers: {
                            "Content-Type": "application/json",
                            "x-user-id": user?.uid || "",
                          },
                          body: JSON.stringify({ returnUrl: window.location.href }),
                        });
                        const oauthData = await oauth.json();
                        if (oauth.ok && oauthData.loginUrl) {
                          window.location.href = oauthData.loginUrl;
                          return;
                        }
                      }
                      throw new Error(data?.error || "Failed to get onboarding link");
                    }
                    // If no Stripe account exists yet, ask region, update flag, create one and redirect to onboarding
                    let isUS: boolean;
                    try {
                      isUS = await askIsUS();
                    } catch {
                      setDashboardLoading(false);
                      return;
                    }
                    try {
                      await ChangeIsUsFlag({ user_id: user?.uid as string, isUs: isUS });
                    } catch (e) {
                      console.warn("Failed to update isUS flag", e);
                    }
                    const onboardRes = await fetch("/api/connect/onboard", {
                      method: "POST",
                      headers: { "Content-Type": "application/json" },
                      body: JSON.stringify({
                        userId: user?.uid,
                        email: user?.email,
                        isUS,
                      }),
                    });
                    const onboardData = await onboardRes.json();
                    if (onboardRes.ok && onboardData.url) {
                      window.location.href = onboardData.url;
                      return;
                    }
                    throw new Error(onboardData?.error || "Failed to create Stripe account");
                  } catch (err) {
                    setError(err instanceof Error ? err.message : "Failed to start onboarding");
                  } finally {
                    setDashboardLoading(false);
                  }
                }}
              >
                Complete Onboarding
              </Button>
            </div>
          )}
        </div>
      </div>
    );
  }

  // User is authenticated but no seller account - show creation or connect-existing options
  if (isAuthenticated && !sellerAccount) {

    return (
      <div className="flex items-center justify-center">
        <Card className="w-full max-w-md">
        <RegionDialog />

          <CardHeader className="text-center">
            <CardTitle className="flex items-center justify-center">
              <Building className="h-6 w-6 mr-2" />
              Connect a Stripe Account
            </CardTitle>
            <CardDescription>
              Welcome {user?.displayName || user?.email}! Choose how to connect your seller account.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {error && (
              <div className="flex items-center p-3 bg-red-50 text-red-700 rounded-lg">
                <AlertCircle className="h-4 w-4 mr-2" />
                {error}
              </div>
            )}

            {success && (
              <div className="flex items-center p-3 bg-green-50 text-green-700 rounded-lg">
                <CheckCircle className="h-4 w-4 mr-2" />
                {success}
              </div>
            )}

            <Tabs
              value={connectMode}
              onValueChange={(v) => setConnectMode(v as "create" | "oauth")}
            >
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="create">Create New</TabsTrigger>
                <TabsTrigger value="oauth">Connect via OAuth</TabsTrigger>
              </TabsList>

              <TabsContent value="create" className="space-y-4 mt-6">
                <Button
                  onClick={handleCreateSellerAccount}
                  disabled={isLoading}
                  className="w-full"
                  size="lg"
                >
                  {isLoading ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Creating Account...
                    </>
                  ) : (
                    <>
                      <CreditCard className="h-4 w-4 mr-2" />
                      Create Stripe Seller Account
                    </>
                  )}
                </Button>
              </TabsContent>

              <TabsContent value="oauth" className="space-y-4 mt-6">
                {/* <div className="bg-blue-50 p-4 rounded-lg mb-4">
                  <h4 className="font-semibold text-blue-900 mb-2">Connect Login (Popup)</h4>
                  <p className="text-sm text-blue-800">
                    Login to existing Stripe account using OAuth popup - no account ID needed
                  </p>
                </div> */}

                <Button
                  onClick={async () => {
                    if (!user?.uid) {
                      setError("User authentication required");
                      return;
                    }

                    try {
                      setIsLoading(true);
                      setError(null);

                      // Use the OAuth popup API
                      const res = await fetch("/api/connect/login-oauth", {
                        method: "POST",
                        headers: {
                          "Content-Type": "application/json",
                          // Pass user identity so the API can authenticate the request
                          "x-user-id": user.uid,
                        },
                        body: JSON.stringify({
                          userId: user.uid,
                          // Default to embedded dashboard after OAuth
                          returnUrl: "/",
                        }),
                      });

                      const data = await res.json();
                      if (!res.ok || !data.loginUrl) {
                        throw new Error(data.error || "Failed to generate OAuth link");
                      }

                      // Redirect in the same tab instead of popup
                      window.location.href = data.loginUrl;
                      return;
                    } catch (err) {
                      setError(err instanceof Error ? err.message : "Failed to start OAuth");
                    } finally {
                      setIsLoading(false);
                    }
                  }}
                  className="w-full"
                  size="lg"
                >
                  {isLoading ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Opening OAuth Popup...
                    </>
                  ) : (
                    <>
                      <Building className="h-4 w-4 mr-2" />
                      Connect via OAuth Popup
                    </>
                  )}
                </Button>
              </TabsContent>
            </Tabs>

            {/* <div>
              <div className="text-center space-y-3">
                <p className="text-sm text-gray-600">Need help finding your Stripe account ID?</p>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() =>
                    window.open("https://dashboard.stripe.com/settings/account", "_blank")
                  }
                >
                  Open Stripe Dashboard
                </Button>
                <p className="text-xs text-gray-500">
                  Your account ID is shown in the top-right corner of your Stripe dashboard
                </p>
              </div>
            </div> */}
          </CardContent>
        </Card>
      </div>
    );
  }

  // User not authenticated - show login/signup
  return (
    <div className=" flex items-center justify-center">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
      <RegionDialog />

          <CardTitle className="flex items-center justify-center">
            <CreditCard className="h-6 w-6 mr-2" />
            Stripe Seller Portal
          </CardTitle>
          <CardDescription>Login to manage your seller account or create a new one</CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs
            value={authMode}
            onValueChange={(value) => setAuthMode(value as "login" | "signup")}
          >
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="login">Login</TabsTrigger>
              <TabsTrigger value="signup">Create Account</TabsTrigger>
            </TabsList>

            <TabsContent value="login" className="space-y-4 mt-6">
              <form onSubmit={handleLogin} className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="login-email">Email</Label>
                  <div className="relative">
                    <Mail className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                    <Input
                      id="login-email"
                      name="email"
                      type="email"
                      value={formData.email}
                      onChange={handleInputChange}
                      placeholder="Enter your email"
                      className="pl-10"
                      required
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="login-password">Password</Label>
                  <div className="relative">
                    <Lock className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                    <Input
                      id="login-password"
                      name="password"
                      type={showPassword ? "text" : "password"}
                      value={formData.password}
                      onChange={handleInputChange}
                      placeholder="Enter your password"
                      className="pl-10 pr-10"
                      required
                    />
                    <button
                      type="button"
                      onClick={() => setShowPassword(!showPassword)}
                      className="absolute right-3 top-3 text-gray-400 hover:text-gray-600"
                    >
                      {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                    </button>
                  </div>
                </div>

                {error && (
                  <div className="flex items-center p-3 bg-red-50 text-red-700 rounded-lg">
                    <AlertCircle className="h-4 w-4 mr-2" />
                    {error}
                  </div>
                )}

                {success && (
                  <div className="flex items-center p-3 bg-green-50 text-green-700 rounded-lg">
                    <CheckCircle className="h-4 w-4 mr-2" />
                    {success}
                  </div>
                )}

                <Button type="submit" disabled={isLoading} className="w-full">
                  {isLoading ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Logging in...
                    </>
                  ) : (
                    "Login"
                  )}
                </Button>
              </form>
            </TabsContent>

            <TabsContent value="signup" className="space-y-4 mt-6">
              <form onSubmit={handleSignup} className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="signup-email">Email</Label>
                  <div className="relative">
                    <Mail className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                    <Input
                      id="signup-email"
                      name="email"
                      type="email"
                      value={formData.email}
                      onChange={handleInputChange}
                      placeholder="Enter your email"
                      className="pl-10"
                      required
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="signup-displayName">Display Name</Label>
                  <div className="relative">
                    <User className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                    <Input
                      id="signup-displayName"
                      name="displayName"
                      type="text"
                      value={formData.displayName}
                      onChange={handleInputChange}
                      placeholder="Your name"
                      className="pl-10"
                      required
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="signup-businessName">Business Name (Optional)</Label>
                  <div className="relative">
                    <Building className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                    <Input
                      id="signup-businessName"
                      name="businessName"
                      type="text"
                      value={formData.businessName}
                      onChange={handleInputChange}
                      placeholder="Your business name"
                      className="pl-10"
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="signup-password">Password</Label>
                  <div className="relative">
                    <Lock className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                    <Input
                      id="signup-password"
                      name="password"
                      type={showPassword ? "text" : "password"}
                      value={formData.password}
                      onChange={handleInputChange}
                      placeholder="Create a password"
                      className="pl-10 pr-10"
                      required
                    />
                    <button
                      type="button"
                      onClick={() => setShowPassword(!showPassword)}
                      className="absolute right-3 top-3 text-gray-400 hover:text-gray-600"
                    >
                      {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                    </button>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="signup-confirmPassword">Confirm Password</Label>
                  <div className="relative">
                    <Lock className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                    <Input
                      id="signup-confirmPassword"
                      name="confirmPassword"
                      type={showPassword ? "text" : "password"}
                      value={formData.confirmPassword}
                      onChange={handleInputChange}
                      placeholder="Confirm your password"
                      className="pl-10"
                      required
                    />
                  </div>
                </div>

                {error && (
                  <div className="flex items-center p-3 bg-red-50 text-red-700 rounded-lg">
                    <AlertCircle className="h-4 w-4 mr-2" />
                    {error}
                  </div>
                )}

                {success && (
                  <div className="flex items-center p-3 bg-green-50 text-green-700 rounded-lg">
                    <CheckCircle className="h-4 w-4 mr-2" />
                    {success}
                  </div>
                )}

                <div className="bg-blue-50 p-3 rounded-lg">
                  <p className="text-xs text-blue-800">
                    By creating an account, you'll automatically get a Stripe seller account to
                    start accepting payments immediately.
                  </p>
                </div>

                <Button type="submit" disabled={isLoading} className="w-full">
                  {isLoading ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Creating Account...
                    </>
                  ) : (
                    "Create Account & Stripe Seller Account"
                  )}
                </Button>
              </form>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
};

export default StripeAuthFlow;
