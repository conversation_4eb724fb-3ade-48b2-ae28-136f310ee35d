import { useCallback, useEffect, useState, useMemo } from "react";
import { getUsersByCategory } from "@/services/usersServices";
import { themes } from "../../../../theme";
import GlobalProfileCard from "@/globalComponents/globalProfileCard";
import useAuth from "@/hook";
import useProfile from "@/hook/profileData";
import LoadingOverlay from "@/components/loadingOverlay";
import { useFilter } from "@/context/FilterContext";
import GlobalProfileCardLens from "@/globalComponents/globalProfileCardLens";
import { sortProfiles } from "@/lib/helper";
import { FollowerManager } from "@/services/followServices";
import {
  AccountsBulkQuery,
  FollowingOrderBy,
  PageSize,
  useAccountsBulkQuery,
  useFollowingQuery,
} from "@/graphql/test/generated";
import { getLocation } from "@/lib/utils";
import { getId } from "@/services/authBridgeService";

const ProfileCardSC = (props: any) => {
  const user = useAuth();
  const { filters } = useFilter();
  const { profileData } = useProfile(user?.userId || "");
  const [categoryData, setCategoryData] = useState<Record<string, any[]>>({});
  const [lensProfiles, setLensProfiles] = useState<any[]>([]);
  const [profilesData, setProfilesData] = useState<AccountsBulkQuery["accountsBulk"]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [filterUpdateTrigger, setFilterUpdateTrigger] = useState<number>(0);

  // Move categories outside component or use useMemo to prevent recreation
  const categories = useMemo(
    () => [
      "Music",
      "Literature",
      "Art",
      "Film & Photography",
      "Theatre & Performance",
      "Multidisciplinary",
      "Groups",
      "Storytelling",
    ],
    []
  );

  // Create stable references for filter arrays to prevent unnecessary re-renders
  const stableAmuznFilter = useMemo(
    () => filters?.amuznProfilesFilter,
    [filters?.amuznProfilesFilter]
  );
  const stableLensFilter = useMemo(
    () => filters?.lensProfilesFilter,
    [filters?.lensProfilesFilter]
  );

  // Watch for filter changes and immediately clear stale data
  useEffect(() => {
    // console.log("ProfilesCardSC: Filter change detected, clearing stale data");
    setCategoryData({});
    setLensProfiles([]);
    setProfilesData([]);
    setFilterUpdateTrigger((prev) => prev + 1);
  }, [stableAmuznFilter, stableLensFilter]);

  // Direct useEffect without useCallback to avoid infinite loops
  useEffect(() => {
    // Don't fetch if we don't have a valid category
    if (!props.themeProperties?.title) {
      console.log("ProfilesCardSC: No category provided, skipping fetch");
      setLoading(false);
      return;
    }

    const fetchAllUsersByCategory = async () => {
      try {
        setLoading(true);

        // Clear existing data immediately when filters change
        setCategoryData({});
        setLensProfiles([]);
        setProfilesData([]);
        setFilterUpdateTrigger((prev) => prev + 1); // Force re-render

        // console.log("ProfilesCardSC: Starting fetch with filters:", {
        //   category: props.themeProperties.title,
        //   userId: user?.userId,
        //   isLogin: user.isLogin,
        //   amuznProfilesFilter: stableAmuznFilter,
        //   lensProfilesFilter: stableLensFilter,
        // });

        const response = await getUsersByCategory({
          category:
            props.themeProperties.title === "My Feed" ? "my-feed" : props.themeProperties.title,
          currentUserId: user?.userId,
          filter: {
            amuzn_user: stableAmuznFilter,
            lens_user: stableLensFilter,
          },
        });

        // console.log("ProfilesCardSC: getUsersByCategory response:", response);

        if (response?.users || response?.lens) {
          const categorizedData: Record<string, any[]> = {};
          let myFeed: any[] = [];

          // Process amuzn users if they exist
          if (Array.isArray(response.users) && response.users.length > 0) {
            // Process categories efficiently
            for (const category of categories) {
              const filteredPosts = response.users.filter((post: any) => {
                const userCategory = post?.categories?.[0];
                if (!post.profile_name) return false;

                return category === "Literature"
                  ? userCategory === category || userCategory === "Storytelling"
                  : userCategory === category;
              });

              categorizedData[category] = filteredPosts;
            }

            // Fetch following data only once if user is logged in
            if (user.isLogin && user.userId && props.themeProperties.title === "My Feed") {
              try {
                myFeed = await FollowerManager.getInstance().GetFollowingsByUserId(user.userId);
                categorizedData["My Feed"] = myFeed;
              } catch (error) {
                console.error("Error fetching following data:", error);
              }
            }
          }

          // Update states synchronously
          setCategoryData(categorizedData);
          setLensProfiles(response.lens || []);

          // console.log("ProfilesCardSC: Data set successfully", {
          //   categorizedData,
          //   lensProfiles: response.lens || [],
          //   currentCategory: props.themeProperties.title,
          // });
        } else {
          // Clear data when no results
          setCategoryData({});
          setLensProfiles([]);
          console.log("ProfilesCardSC: No data found, clearing state");
        }
      } catch (error) {
        console.error("ProfilesCardSC: Error fetching posts:", error);
        setCategoryData({});
        setLensProfiles([]);
      } finally {
        setLoading(false);
        console.log("ProfilesCardSC: Loading set to false");
      }
    };

    fetchAllUsersByCategory();
  }, [
    props.themeProperties.title,
    user?.userId,
    user.isLogin,
    stableAmuznFilter,
    stableLensFilter,
    categories,
  ]);

  // My Feed lens profiles state
  const [profilesmy, setProfilesMy] = useState<
    Array<{
      localName: string;
    }>
  >([]);

  const { data: profileDataByLensId } = useAccountsBulkQuery(
    {
      request: {
        usernames:
          props.themeProperties.title === "My Feed"
            ? profilesmy
            : lensProfiles?.map((name: any) => ({ localName: name.profile_name })) || [],
      },
    },
    {
      refetchOnWindowFocus: false,
      enabled:
        props.themeProperties.title === "My Feed"
          ? profilesmy.length > 0
          : lensProfiles?.length > 0,
      // Add key to force refetch when filters change
      queryKey: [
        "accountsBulk",
        props.themeProperties.title,
        JSON.stringify(stableAmuznFilter),
        JSON.stringify(stableLensFilter),
        lensProfiles?.length || 0,
      ],
    }
  );

  useEffect(() => {
    if (profileDataByLensId) {
      setProfilesData(profileDataByLensId?.accountsBulk || []);
      // console.log("ProfilesCardSC: Lens profiles data updated:", profileDataByLensId?.accountsBulk);
    } else {
      // Clear lens profiles data when query is disabled or no data
      setProfilesData([]);
      console.log("ProfilesCardSC: Lens profiles data cleared");
    }
  }, [profileDataByLensId, stableAmuznFilter, stableLensFilter]);

  // console.log("ProfilesCardSC: Current state for merging:", {
  //   currentCategory: props.themeProperties.title,
  //   categoryData: categoryData,
  //   categoryDataForCurrentCategory: categoryData[props.themeProperties.title],
  //   lensProfiles: lensProfiles,
  //   profilesData: profilesData,
  //   stableAmuznFilter,
  //   stableLensFilter,
  // });

  const amuznProfilesForCategory = categoryData[props.themeProperties.title] || [];
  const lensProfilesForCategory = profilesData || [];

  const mergedProfiles = useMemo(() => {
    // console.log("ProfilesCardSC: Recalculating mergedProfiles", {
    //   filterUpdateTrigger,
    //   amuznCount: amuznProfilesForCategory.length,
    //   lensCount: lensProfilesForCategory.length,
    // });

    return sortProfiles(
      [
        ...amuznProfilesForCategory.map((item) => ({
          ...item,
          profile_name: item.profile_name || "Profile Name*",
          lensProfile: false, // Mark as amuzn profile
        })),
        ...lensProfilesForCategory.map((profile) => ({
          id: profile.username?.localName,
          profile_name: profile.metadata?.name || "Profile Name*",
          avatar: profile?.metadata?.picture || "",
          location: getLocation(profile.metadata?.attributes) || profile.username?.localName,
          isFollow: profile?.operations?.isFollowedByMe,
          lensProfile: true, // Mark lens profiles
        })),
      ],
      "profile_name"
    );
  }, [amuznProfilesForCategory, lensProfilesForCategory, filterUpdateTrigger]);

  // console.log("ProfilesCardSC: mergedProfiles result:", {
  //   amuznCount: amuznProfilesForCategory.length,
  //   lensCount: lensProfilesForCategory.length,
  //   totalMerged: mergedProfiles.length,
  //   mergedProfiles,
  // });

  // My Feed lens profile management
  const [userId, setUserId] = useState("");

  const getLensUserId = useCallback(async () => {
    if (!user.userId) return;

    try {
      const resp = await getId({ id: user.userId });
      if (resp) {
        setUserId(resp?.lens_code);
      }
    } catch (error) {
      console.log("Error getting lens user ID:", error);
    }
  }, [user.userId]);

  useEffect(() => {
    getLensUserId();
  }, [getLensUserId]);

  const { data: following } = useFollowingQuery(
    {
      request: {
        account: userId, // address
        pageSize: PageSize.Fifty,
        orderBy: FollowingOrderBy.Desc,
      },
    },
    {
      refetchOnWindowFocus: false,
      enabled: !!userId,
    }
  );

  interface FollowingItem {
    localName: any;
  }

  const [allFollowing, setAllFollowing] = useState<FollowingItem[]>([]);

  useEffect(() => {
    if (
      following?.following?.items &&
      following?.following?.items.length > 0 &&
      props.themeProperties.title === "My Feed"
    ) {
      const newArray: FollowingItem[] = following.following.items.map((item: any) => ({
        localName: item.following.username?.localName,
      }));

      setAllFollowing((prev) => [...prev, ...newArray]);
    }
  }, [following, props.themeProperties.title]);

  useEffect(() => {
    if (allFollowing) {
      setProfilesMy(allFollowing);
    }
  }, [allFollowing]);
  return (
    <>
      {loading ? (
        <LoadingOverlay isLoading={loading} />
      ) : (
        <div className="w-full mt-0">
          {Object.entries(themes).map(([, themeProperties], indexM) => (
            <div key={indexM}>
              {props.themeProperties.title === themeProperties.title && (
                <div className="grid grid-cols-4 max-md:grid-cols-1 max-lg:grid-cols-2 gap-3 mt-4">
                  {(() => {
                    const currentCategory = themeProperties.title;

                    // Check if filters are applied and if current category is in selected categories
                    if (filters.categories && filters.categories.length > 0) {
                      if (!filters.categories.includes(currentCategory)) {
                        // Current category is not in selected filters, show no data
                        return (
                          <div className="col-span-4 w-full mt-2 p-4 bg-gray-50 rounded-md border border-gray-200 text-center">
                            <p className="text-gray-500">No profiles found in this category.</p>
                          </div>
                        );
                      }
                    }

                    return mergedProfiles.length > 0 ? (
                      mergedProfiles.map((post, index) => (
                        <div key={index} className="mt-0">
                          {themeProperties.title === "My Feed" && !post.lensProfile ? (
                            Object.entries(themes).map(([_, innerThemeProperties], idx) => (
                              <div key={idx}>
                                {innerThemeProperties.title ===
                                  (post?.categories[0] === "Storytelling"
                                    ? "Literature"
                                    : post?.categories[0]) && (
                                  <GlobalProfileCard
                                    themeProperties={innerThemeProperties}
                                    isFollow={!profileData?.followers.includes(post.id)}
                                    location={post.location}
                                    profile_name={post.profile_name}
                                    avatar={post.avatar}
                                    id={post.id}
                                  />
                                  // <p>{post.profile_name}hii</p>
                                )}
                              </div>
                            ))
                          ) : themeProperties.title === "My Feed" && post.lensProfile ? (
                            <div>
                              <GlobalProfileCardLens
                                themeProperties={themeProperties}
                                isFollow={post.isFollow}
                                location={post.location}
                                profile_name={post.profile_name}
                                avatar={post.avatar}
                                id={post.id}
                              />
                            </div>
                          ) : post.lensProfile ? (
                            <GlobalProfileCardLens
                              themeProperties={themeProperties}
                              isFollow={post.isFollow}
                              location={post.location}
                              profile_name={post.profile_name}
                              avatar={post.avatar}
                              id={post.id}
                            />
                          ) : (
                            // <p>{post.profile_name} lens</p>
                            <GlobalProfileCard
                              themeProperties={themeProperties}
                              isFollow={!profileData?.followers.includes(post.id)}
                              location={post.location}
                              profile_name={post.profile_name}
                              avatar={post.avatar}
                              id={post.id}
                            />
                            // <p>{post.profile_name}hiii</p>
                          )}
                        </div>
                      ))
                    ) : (
                      <p className="text-gray-500">No Profile available in this category.</p>
                    );
                  })()}
                </div>
              )}
            </div>
          ))}
        </div>
      )}
    </>
  );
};

export default ProfileCardSC;
